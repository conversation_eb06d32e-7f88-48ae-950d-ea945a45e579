#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设备管理程序 - PyQt6版本
功能1:1复刻Web版本，优化性能和用户体验
"""

import sys
import json
import os
import subprocess
import threading
import time
import datetime
import concurrent.futures


from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QGridLayout, QLabel, QPushButton, QFrame, QScrollArea, QDialog,
    QFormLayout, QLineEdit, QComboBox, QCheckBox, QTextEdit, QMessageBox,
    QMenu, QSplitter, QTabWidget, QListWidgetItem, QTableWidget,
    QTableWidgetItem, QHeaderView, QListWidget, QSpinBox
)
from PyQt6.QtCore import (
    Qt, QTimer, pyqtSignal, QPoint, QObject, QMimeData
)
from PyQt6.QtGui import QAction, QDrag

# Windows API相关导入
try:
    import ctypes  # noqa: F401
    WIN_API_AVAILABLE = True
except ImportError:
    WIN_API_AVAILABLE = False


class LogSignals(QObject):
    """日志信号类，用于线程安全的日志记录"""
    log_message = pyqtSignal(str, str)  # message, device_name
    battery_level_updated = pyqtSignal(str, int, bool)  # device_name, battery_level, is_charging


class ClosableTabWidget(QTabWidget):
    """支持双击关闭标签页的QTabWidget"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.main_window = None
        # 连接标签栏的双击信号
        self.tabBar().tabBarDoubleClicked.connect(self.on_tab_double_clicked)

    def set_main_window(self, main_window):
        """设置主窗口引用"""
        self.main_window = main_window

    def on_tab_double_clicked(self, index):
        """处理标签页双击事件"""
        if index >= 0:
            tab_text = self.tabText(index)
            # 不允许关闭"程序日志"标签页
            if tab_text != "程序日志":
                self.close_tab(index, tab_text)

    def close_tab(self, index: int, tab_text: str):
        """关闭指定的标签页"""
        try:
            if self.main_window:
                # 从设备日志字典中移除
                if tab_text in self.main_window.device_logs:
                    del self.main_window.device_logs[tab_text]

                # 移除标签页
                self.removeTab(index)

                # 记录日志
                self.main_window.log(f"已关闭设备 {tab_text} 的日志标签页")

        except Exception as e:
            print(f"关闭标签页时出错: {str(e)}")
            if self.main_window:
                self.main_window.log(f"关闭标签页时出错: {str(e)}")

class DeviceCard(QFrame):
    """设备卡片组件"""
    
    # 信号定义
    clicked = pyqtSignal(str)  # 设备名称
    doubleClicked = pyqtSignal(str)
    rightClicked = pyqtSignal(str, QPoint)
    deviceReordered = pyqtSignal(str, str)  # 设备重排序信号 (source, target)
    
    def __init__(self, device_data: dict, parent=None, card_height=160):
        super().__init__(parent)
        self.device_data = device_data
        self.selected = False
        self.card_height = card_height

        # 拖拽相关属性
        self.drag_start_position = None
        self.setAcceptDrops(True)  # 接受拖拽

        self.setup_ui()
        self.setup_style()
        
    def setup_ui(self):
        """设置UI布局 - 新的布局设计"""
        # 设置动态高度，宽度由布局自动调整
        self.setFixedHeight(self.card_height)
        self.setMinimumWidth(180)  # 增加最小宽度以适应新布局
        self.setFrameStyle(QFrame.Shape.Box)

        # 主布局
        main_layout = QVBoxLayout(self)
        margin = max(8, min(15, self.card_height // 12))
        main_layout.setContentsMargins(margin, margin, margin, margin)
        main_layout.setSpacing(8)

        # 上半部分：设备图标区域（整个卡片宽度居中）+ 右侧状态按钮（悬浮叠加）
        top_widget = QWidget()
        top_widget.setFixedHeight(int(self.card_height * 0.7))  # 上半部分占70%高度
        top_widget.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, True)  # 让鼠标事件穿透

        # 设备图标区域（无边框，在整个卡片宽度中居中显示）
        icon_size = min(int(self.card_height * 0.6), 80)  # 图标大小

        # 使用布局来实现在整个卡片宽度中的真正居中（不被状态按钮影响）
        icon_layout = QHBoxLayout(top_widget)
        icon_layout.setContentsMargins(0, 10, 0, 10)  # 不留出状态按钮空间，让图标在整个宽度中居中

        # 添加左侧弹性空间
        icon_layout.addStretch()

        self.icon_label = QLabel()
        self.icon_label.setFixedSize(icon_size, icon_size)
        self.icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.icon_label.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, True)  # 让鼠标事件穿透
        self.update_device_icon()  # 根据状态设置图标

        icon_layout.addWidget(self.icon_label)

        # 添加右侧弹性空间
        icon_layout.addStretch()

        # 电池电量显示（悬浮在右上角）
        self.battery_label = QLabel()
        self.battery_label.setParent(self)  # 直接设置为卡片的子组件
        self.battery_label.setFixedSize(35, 20)  # 固定大小
        self.battery_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.battery_label.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, True)  # 让鼠标事件穿透
        self.battery_label.setStyleSheet("""
            QLabel {
                background: rgba(0, 0, 0, 0.7);
                color: white;
                border-radius: 10px;
                font-size: 10px;
                font-weight: bold;
                padding: 2px;
            }
        """)
        self.battery_label.hide()  # 初始隐藏，有电量数据时才显示

        # 充电闪电符号（悬浮在电池标签底部）
        self.charging_label = QLabel()
        self.charging_label.setParent(self)  # 直接设置为卡片的子组件
        self.charging_label.setFixedSize(12, 12)  # 小尺寸
        self.charging_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.charging_label.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, True)  # 让鼠标事件穿透
        self.charging_label.setText("⚡")
        self.charging_label.setStyleSheet("""
            QLabel {
                background: rgba(255, 215, 0, 0.9);
                color: #FFD700;
                border-radius: 6px;
                font-size: 8px;
                font-weight: bold;
                padding: 1px;
            }
        """)
        self.charging_label.hide()  # 初始隐藏，充电时才显示

        # 立即定位电池标签到右上角
        margin = max(8, min(15, self.card_height // 12))
        self.battery_label.move(self.width() - self.battery_label.width() - margin - 5, margin + 5)
        # 充电符号位于电池标签右下角
        self.charging_label.move(
            self.battery_label.x() + self.battery_label.width() - 8,
            self.battery_label.y() + self.battery_label.height() - 8
        )



        # 下半部分：设备名称区域（无边框，紧凑高度）
        name_font_size = min(12, max(9, self.card_height // 15))
        name_height = name_font_size + 8  # 字体高度 + 少量内边距

        self.name_label = QLabel(self.device_data.get('sb_name', '未知设备'))
        self.name_label.setFixedHeight(name_height)
        self.name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.name_label.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, True)  # 让鼠标事件穿透
        self.name_label.setStyleSheet(f"""
            QLabel {{
                font-size: {name_font_size}px;
                font-weight: bold;
                color: #333;
                background: transparent;
                border: none;
                padding: 2px;
            }}
        """)

        # 添加到主布局
        main_layout.addWidget(top_widget)  # 上半部分
        main_layout.addWidget(self.name_label)  # 下半部分，自动调整高度

        # 设置工具提示显示设备详细信息
        self.setup_tooltip()







    def update_device_icon(self):
        """根据设备状态更新图标"""
        # 检查图标标签是否已创建
        if not hasattr(self, 'icon_label'):
            return

        # 获取设备状态
        adb_connected = self.device_data.get('adb_connected', False)
        task_running = self.device_data.get('task_running', False)
        display_active = self.device_data.get('display_active', False)

        # 根据设备状态选择不同的图标
        if task_running:
            # 运行中：使用齿轮图标表示正在执行任务
            icon_text = "⚙️"
            icon_color = "stop:0 #FF6B6B, stop:1 #FF8E53"  # 红橙渐变
        elif display_active:
            # 显示中：使用屏幕图标表示正在显示
            icon_text = "🖥️"
            icon_color = "stop:0 #4ECDC4, stop:1 #44A08D"  # 青绿渐变
        else:
            # 空闲：使用手机图标表示空闲状态
            icon_text = "📱"
            icon_color = "stop:0 #667eea, stop:1 #764ba2"  # 蓝紫渐变

        # 如果ADB未连接，使用灰色
        if not adb_connected:
            icon_color = "stop:0 #888888, stop:1 #666666"  # 灰色渐变

        # 根据卡片高度动态调整图标大小，移除URL后可以更大
        margin = max(8, min(15, self.card_height // 12))
        spacing = max(4, min(10, self.card_height // 20))
        available_height = self.card_height - 2 * margin - 2 * spacing - 30  # 预留名称和状态的空间
        icon_size = min(80, max(50, available_height // 2))
        font_size = min(32, max(20, icon_size // 2))

        self.icon_label.setText(icon_text)
        self.icon_label.setStyleSheet(f"""
            QLabel {{
                font-size: {font_size}px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    {icon_color});
                border-radius: {icon_size//5}px;
                color: {'#cccccc' if not adb_connected else 'white'};
                min-width: {icon_size}px;
                min-height: {icon_size}px;
                max-width: {icon_size}px;
                max-height: {icon_size}px;
            }}
        """)

    def update_battery_display(self):
        """更新电池电量显示"""
        if not hasattr(self, 'battery_label'):
            return

        battery_level = self.device_data.get('battery_level', None)
        adb_connected = self.device_data.get('adb_connected', False)
        is_charging = self.device_data.get('is_charging', False)

        if battery_level is not None and adb_connected:
            # 根据电量设置颜色
            if battery_level >= 60:
                color = "#4CAF50"  # 绿色
            elif battery_level >= 30:
                color = "#FF9800"  # 橙色
            else:
                color = "#F44336"  # 红色

            self.battery_label.setText(f"{battery_level}%")
            self.battery_label.setStyleSheet(f"""
                QLabel {{
                    background: rgba(0, 0, 0, 0.7);
                    color: {color};
                    border-radius: 10px;
                    font-size: 10px;
                    font-weight: bold;
                    padding: 2px;
                    border: 1px solid rgba(255, 255, 255, 0.3);
                }}
            """)
            # 确保位置正确
            margin = max(8, min(15, self.card_height // 12))
            self.battery_label.move(self.width() - self.battery_label.width() - margin - 5, margin + 5)
            self.battery_label.show()
            self.battery_label.raise_()  # 确保在最上层

            # 更新充电状态显示
            if hasattr(self, 'charging_label'):
                if is_charging:
                    # 定位充电符号到电池标签右下角
                    self.charging_label.move(
                        self.battery_label.x() + self.battery_label.width() - 8,
                        self.battery_label.y() + self.battery_label.height() - 8
                    )
                    self.charging_label.show()
                    self.charging_label.raise_()  # 确保在最上层
                else:
                    self.charging_label.hide()
        else:
            self.battery_label.hide()
            if hasattr(self, 'charging_label'):
                self.charging_label.hide()

    def setup_tooltip(self):
        """设置工具提示显示设备详细信息"""
        device_name = self.device_data.get('sb_name', '未知设备')
        current_url = self.get_current_url()

        # 获取设备的其他信息
        device_info = []
        device_info.append(f"设备名称: {device_name}")
        device_info.append(f"连接地址: {current_url}")

        # 添加设备配置信息
        if 'device_config' in self.device_data:
            config = self.device_data['device_config']
            if 'brand' in config:
                device_info.append(f"品牌: {config['brand']}")
            if 'model' in config:
                device_info.append(f"型号: {config['model']}")
            if 'android_version' in config:
                device_info.append(f"Android版本: {config['android_version']}")

        # 添加运行状态信息
        adb_status = "已连接" if self.device_data.get('adb_connected', False) else "未连接"
        display_status = "运行中" if self.device_data.get('display_active', False) else "未运行"
        task_status = "执行中" if self.device_data.get('task_running', False) else "空闲"

        device_info.append(f"ADB状态: {adb_status}")
        device_info.append(f"显示状态: {display_status}")
        device_info.append(f"任务状态: {task_status}")

        # 添加电池电量信息
        battery_level = self.device_data.get('battery_level', None)
        is_charging = self.device_data.get('is_charging', False)
        if battery_level is not None:
            charging_text = " (充电中)" if is_charging else ""
            device_info.append(f"电池电量: {battery_level}%{charging_text}")

        # 添加卡账信息
        # 查找主窗口
        main_window = None
        widget = self
        while widget:
            if hasattr(widget, 'get_device_phone_cards') and hasattr(widget, 'get_device_app_accounts'):
                main_window = widget
                break
            widget = widget.parent()

        if main_window:
            # 获取关联的手机卡
            phone_cards = main_window.get_device_phone_cards(device_name)
            if phone_cards:
                device_info.append("")  # 空行分隔
                device_info.append("📱 关联手机卡:")
                for card_data in phone_cards.values():
                    phone_number = card_data.get('phone_number', '未知')
                    tags = card_data.get('tags', {})
                    tag_text = ', '.join([f"{k}:{v}" for k, v in tags.items()]) if tags else '无标签'
                    device_info.append(f"  📞 {phone_number} ({tag_text})")

            # 获取关联的APP账号
            app_accounts = main_window.get_device_app_accounts(device_name)
            if app_accounts:
                if not phone_cards:  # 如果没有手机卡信息，添加空行分隔
                    device_info.append("")
                device_info.append("📱 关联APP账号:")
                for account_data in app_accounts.values():
                    app_name = account_data.get('app_name', '未知')
                    phone_number = account_data.get('phone_number', '')
                    app_type = account_data.get('app_type', 'original')
                    type_text = "原" if app_type == "original" else "分身"
                    phone_text = f" [手机号:{phone_number}]" if phone_number else " [无手机号]"
                    tags = account_data.get('tags', {})
                    tag_text = ', '.join([f"{k}:{v}" for k, v in tags.items()]) if tags else '无标签'
                    device_info.append(f"  📱 {app_name}({type_text}){phone_text} ({tag_text})")

        # 设置工具提示
        tooltip_text = "\n".join(device_info)
        self.setToolTip(tooltip_text)

    def resizeEvent(self, event):
        """重写resize事件，重新定位悬浮元素"""
        super().resizeEvent(event)

        # 图标现在使用布局自动居中，不需要手动定位

        # 定位电池电量标签到右上角
        if hasattr(self, 'battery_label'):
            margin = max(8, min(15, self.card_height // 12))
            self.battery_label.move(self.width() - self.battery_label.width() - margin - 5, margin + 5)

            # 定位充电符号到电池标签右下角
            if hasattr(self, 'charging_label'):
                self.charging_label.move(
                    self.battery_label.x() + self.battery_label.width() - 8,
                    self.battery_label.y() + self.battery_label.height() - 8
                )



    def setup_style(self):
        """设置样式"""
        self.setStyleSheet("""
            DeviceCard {
                background: white;
                border: 2px solid transparent;
                border-radius: 12px;
            }
            DeviceCard:hover {
                border-color: #4facfe;
                background: #f8f9ff;
            }
        """)
        
    def get_current_url(self) -> str:
        """获取当前URL"""
        urls = self.device_data.get('sb_urls', [])
        current_index = self.device_data.get('current_url_index', 0)
        if urls and current_index < len(urls):
            return urls[current_index]
        return "无URL"
        

        

    def update_data(self, device_data: dict):
        """更新设备数据"""
        try:
            self.device_data = device_data

            # 使用QTimer确保在主线程中更新UI
            from PyQt6.QtCore import QTimer
            QTimer.singleShot(0, lambda: self._update_ui_safe())

        except Exception as e:
            print(f"更新设备数据失败: {str(e)}")

    def _update_ui_safe(self):
        """安全的UI更新方法"""
        try:
            if hasattr(self, 'name_label') and self.name_label:
                self.name_label.setText(self.device_data.get('sb_name', '未知设备'))
            self.update_device_icon()
            self.update_battery_display()
            if hasattr(self, 'setup_tooltip'):
                self.setup_tooltip()
        except Exception as e:
            print(f"UI更新失败: {str(e)}")
        
    def set_selected(self, selected: bool):
        """设置选中状态"""
        self.selected = selected
        if selected:
            self.setStyleSheet("""
                DeviceCard {
                    background: white;
                    border: 2px solid #4facfe;
                    border-radius: 12px;
                    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.3);
                }
            """)
        else:
            self.setup_style()
            
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_start_position = event.position().toPoint()
            print(f"[DEBUG] 鼠标按下: {self.device_data.get('sb_name', '')} at {self.drag_start_position}")
            self.clicked.emit(self.device_data.get('sb_name', ''))
        elif event.button() == Qt.MouseButton.RightButton:
            self.rightClicked.emit(self.device_data.get('sb_name', ''), event.globalPosition().toPoint())
            
    def mouseMoveEvent(self, event):
        """鼠标移动事件 - 处理拖拽"""
        if not (event.buttons() & Qt.MouseButton.LeftButton):
            return

        if not self.drag_start_position:
            return

        # 检查是否移动了足够的距离来开始拖拽
        current_pos = event.position().toPoint()
        distance = (current_pos - self.drag_start_position).manhattanLength()
        min_distance = QApplication.startDragDistance()

        print(f"[DEBUG] 鼠标移动: {self.device_data.get('sb_name', '')} 距离={distance}, 最小距离={min_distance}")

        if distance < min_distance:
            return

        print(f"[DEBUG] 开始拖拽: {self.device_data.get('sb_name', '')}")
        # 开始拖拽操作
        self.start_drag()

    def start_drag(self):
        """开始拖拽操作"""
        device_name = self.device_data.get('sb_name', '')
        print(f"[DEBUG] 创建拖拽对象: {device_name}")

        drag = QDrag(self)
        mime_data = QMimeData()

        # 设置拖拽数据
        mime_data.setText(device_name)
        mime_data.setData("application/x-device-card", device_name.encode())

        drag.setMimeData(mime_data)

        print(f"[DEBUG] 执行拖拽: {device_name}")
        # 执行拖拽
        result = drag.exec(Qt.DropAction.MoveAction)
        print(f"[DEBUG] 拖拽结果: {result}")

    def dragEnterEvent(self, event):
        """拖拽进入事件"""
        target_device = self.device_data.get('sb_name', '')
        print(f"[DEBUG] 拖拽进入: {target_device}")
        if event.mimeData().hasFormat("application/x-device-card"):
            print(f"[DEBUG] 接受拖拽进入: {target_device}")
            event.acceptProposedAction()
        else:
            event.ignore()

    def dragMoveEvent(self, event):
        """拖拽移动事件"""
        if event.mimeData().hasFormat("application/x-device-card"):
            event.acceptProposedAction()
        else:
            event.ignore()

    def dropEvent(self, event):
        """拖拽放置事件"""
        target_device = self.device_data.get('sb_name', '')
        print(f"[DEBUG] 拖拽放置到: {target_device}")

        if event.mimeData().hasFormat("application/x-device-card"):
            source_device = event.mimeData().data("application/x-device-card").data().decode()
            print(f"[DEBUG] 拖拽操作: {source_device} -> {target_device}")

            # 发射设备重排序信号
            self.deviceReordered.emit(source_device, target_device)

            event.acceptProposedAction()
        else:
            event.ignore()

    def mouseDoubleClickEvent(self, event):
        """鼠标双击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.doubleClicked.emit(self.device_data.get('sb_name', ''))


class RunTaskDialog(QDialog):
    """运行任务对话框"""

    def __init__(self, device_data: dict = None, parent=None):
        super().__init__(parent)
        self.device_data = device_data or {}
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("运行任务配置")
        self.setFixedSize(400, 280)
        self.setModal(True)

        layout = QVBoxLayout(self)
        layout.setSpacing(15)

        # 设备信息显示
        info_layout = QVBoxLayout()
        device_name = self.device_data.get('sb_name', '未知设备')
        device_url = self.get_current_url()

        info_label = QLabel(f"设备: {device_name}")
        info_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #333;")
        info_layout.addWidget(info_label)

        url_label = QLabel(f"连接地址: {device_url}")
        url_label.setStyleSheet("color: #666; font-size: 12px;")
        info_layout.addWidget(url_label)

        layout.addLayout(info_layout)

        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFrameShadow(QFrame.Shadow.Sunken)
        layout.addWidget(line)

        # 表单布局
        form_layout = QFormLayout()
        form_layout.setSpacing(15)

        # 启动时开启应用
        self.need_open_app = QCheckBox("启动时开启应用")
        self.need_open_app.setStyleSheet("font-size: 13px;")
        form_layout.addRow("", self.need_open_app)

        # 启用分身模式
        self.is_clone = QCheckBox("启用分身模式")
        self.is_clone.setStyleSheet("font-size: 13px;")
        form_layout.addRow("", self.is_clone)

        # 执行动作
        self.action_combo = QComboBox()
        # 从父窗口获取动作列表
        parent_window = self.parent()
        if hasattr(parent_window, 'action_list'):
            self.action_combo.addItems(parent_window.action_list)
        else:
            self.action_combo.addItems(["do_job", "do_test", "monitor", "open_tmall"])
        self.action_combo.setStyleSheet("font-size: 13px;")
        form_layout.addRow("执行动作:", self.action_combo)

        layout.addLayout(form_layout)

        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)

        self.run_btn = QPushButton("开始运行")
        self.run_btn.clicked.connect(self.accept)
        self.run_btn.setDefault(True)

        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.run_btn)

        layout.addLayout(button_layout)

        # 设置样式
        self.setStyleSheet("""
            QDialog {
                background: white;
            }
            QLineEdit, QTextEdit, QComboBox {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 13px;
            }
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
                border-color: #4facfe;
            }
            QCheckBox {
                font-size: 13px;
                color: #333;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #ddd;
                border-radius: 3px;
                background: white;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #4facfe;
                border-radius: 3px;
                background: #4facfe;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
            }
            QPushButton {
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                font-size: 13px;
                font-weight: 500;
                min-width: 80px;
            }
            QPushButton#run_btn {
                background: #4facfe;
                color: white;
            }
            QPushButton#run_btn:hover {
                background: #3d8bfe;
            }
            QPushButton#cancel_btn {
                background: #6c757d;
                color: white;
            }
            QPushButton#cancel_btn:hover {
                background: #5a6268;
            }
        """)

        self.run_btn.setObjectName("run_btn")
        self.cancel_btn.setObjectName("cancel_btn")

    def get_current_url(self) -> str:
        """获取当前URL"""
        urls = self.device_data.get('sb_urls', [])
        current_index = self.device_data.get('current_url_index', 0)
        if urls and current_index < len(urls):
            return urls[current_index]
        return "无URL"

    def load_data(self):
        """加载设备数据到表单"""
        # 优先使用上次的配置，如果没有则使用设备配置
        parent_window = self.parent()
        if hasattr(parent_window, 'last_run_config'):
            last_config = parent_window.last_run_config
            self.need_open_app.setChecked(last_config.get('need_open_app', False))
            self.is_clone.setChecked(last_config.get('is_clone', False))

            action = last_config.get('action', 'do_job')
            index = self.action_combo.findText(action)
            if index >= 0:
                self.action_combo.setCurrentIndex(index)
        elif self.device_data:
            # 如果没有上次配置，则使用设备配置
            self.need_open_app.setChecked(self.device_data.get('need_open_app', False))
            self.is_clone.setChecked(self.device_data.get('is_clone', False))

            action = self.device_data.get('action', 'do_job')
            index = self.action_combo.findText(action)
            if index >= 0:
                self.action_combo.setCurrentIndex(index)

    def get_run_config(self) -> dict:
        """获取运行配置"""
        return {
            'need_open_app': self.need_open_app.isChecked(),
            'is_clone': self.is_clone.isChecked(),
            'action': self.action_combo.currentText()
        }

    def setup_multi_device_info(self, device_list: list):
        """设置多设备信息显示"""
        # 找到主布局
        main_layout = self.layout()

        # 找到并隐藏原有的设备信息布局（第一个布局项）
        info_layout_item = main_layout.itemAt(0)
        if info_layout_item and info_layout_item.layout():
            info_layout = info_layout_item.layout()
            # 隐藏原有的单设备信息
            for i in range(info_layout.count()):
                item = info_layout.itemAt(i)
                if item and item.widget():
                    item.widget().hide()

        # 创建新的多设备信息布局
        from PyQt6.QtWidgets import QVBoxLayout, QLabel
        multi_info_layout = QVBoxLayout()

        # 添加多设备标题
        title_label = QLabel(f"批量运行配置 - {len(device_list)}个设备")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #333;")
        multi_info_layout.addWidget(title_label)

        # 显示设备列表
        if len(device_list) <= 5:
            # 设备数量较少时，显示所有设备名称
            device_names = "、".join(device_list)
            devices_label = QLabel(f"选中设备: {device_names}")
        else:
            # 设备数量较多时，显示前几个和总数
            shown_devices = "、".join(device_list[:3])
            devices_label = QLabel(f"选中设备: {shown_devices}等{len(device_list)}个设备")

        devices_label.setStyleSheet("color: #666; font-size: 12px; margin-top: 5px;")
        devices_label.setWordWrap(True)  # 允许换行
        multi_info_layout.addWidget(devices_label)

        # 添加说明文字
        note_label = QLabel("注意: 相同的配置将应用到所有选中的设备")
        note_label.setStyleSheet("color: #888; font-size: 11px; font-style: italic; margin-top: 5px;")
        multi_info_layout.addWidget(note_label)

        # 将新的多设备信息布局插入到主布局的开头
        main_layout.insertLayout(0, multi_info_layout)


class DeviceEditDialog(QDialog):
    """设备编辑对话框"""

    def __init__(self, device_data: dict = None, parent=None):
        super().__init__(parent)
        self.device_data = device_data or {}
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("设备配置")
        self.setMinimumSize(400, 300)  # 设置最小尺寸
        self.resize(400, 350)  # 设置初始尺寸，允许用户调整
        self.setModal(False)  # 改为非模态，允许用户操作主程序

        layout = QVBoxLayout(self)
        layout.setSpacing(15)

        # 表单布局
        form_layout = QFormLayout()
        form_layout.setSpacing(10)

        # 设备名称
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("请输入设备名称")
        form_layout.addRow("设备名称:", self.name_edit)

        # 设备URL（支持多个）
        self.url_edit = QTextEdit()
        self.url_edit.setMaximumHeight(80)
        self.url_edit.setPlaceholderText("每行一个URL，例如:\n192.168.1.100:5555\n192.168.1.101:5555")
        form_layout.addRow("设备URL:", self.url_edit)

        # 隐藏的字段 - 创建但不显示
        self.need_open_app = QCheckBox("启动时开启应用")
        self.need_open_app.hide()  # 隐藏

        self.is_clone = QCheckBox("启用分身模式")
        self.is_clone.hide()  # 隐藏

        self.action_combo = QComboBox()
        # 从父窗口获取动作列表
        parent_window = self.parent()
        if hasattr(parent_window, 'action_list'):
            self.action_combo.addItems(parent_window.action_list)
        else:
            self.action_combo.addItems(["do_job", "do_test", "monitor", "open_tmall"])
        self.action_combo.hide()  # 隐藏

        # 备注
        self.notes_edit = QTextEdit()
        self.notes_edit.setMinimumHeight(40)  # 缩小最小高度
        self.notes_edit.setMaximumHeight(80)  # 缩小最大高度
        self.notes_edit.setPlaceholderText("设备备注信息...")
        form_layout.addRow("备注:", self.notes_edit)

        layout.addLayout(form_layout)

        # 卡账信息区域
        card_info_layout = QVBoxLayout()

        # 卡账信息标题和按钮
        card_header_layout = QHBoxLayout()
        card_info_label = QLabel("关联卡账信息:")
        card_info_label.setStyleSheet("font-weight: bold; color: #333; font-size: 13px;")
        card_header_layout.addWidget(card_info_label)

        card_header_layout.addStretch()

        # 编辑手机卡按钮
        self.edit_phone_btn = QPushButton("📱 编辑手机卡")
        self.edit_phone_btn.clicked.connect(self.edit_phone_cards)
        self.edit_phone_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        card_header_layout.addWidget(self.edit_phone_btn)

        # 编辑账号信息按钮
        self.edit_account_btn = QPushButton("📱 编辑账号信息")
        self.edit_account_btn.clicked.connect(self.edit_app_accounts)
        self.edit_account_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        card_header_layout.addWidget(self.edit_account_btn)

        card_info_layout.addLayout(card_header_layout)

        # 卡账信息显示区域
        self.card_info_text = QTextEdit()
        self.card_info_text.setReadOnly(True)
        self.card_info_text.setMaximumHeight(120)
        self.card_info_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                background-color: #f8f9fa;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 11px;
                color: #333;
                padding: 8px;
            }
        """)
        card_info_layout.addWidget(self.card_info_text)

        layout.addLayout(card_info_layout)

        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)

        self.save_btn = QPushButton("保存")
        self.save_btn.clicked.connect(self.accept)
        self.save_btn.setDefault(True)

        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.save_btn)

        layout.addLayout(button_layout)

        # 设置样式
        self.setStyleSheet("""
            QDialog {
                background: white;
            }
            QLineEdit, QTextEdit, QComboBox {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 13px;
            }
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
                border-color: #4facfe;
            }
            QPushButton {
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                font-size: 13px;
                font-weight: 500;
            }
            QPushButton#save_btn {
                background: #4facfe;
                color: white;
            }
            QPushButton#save_btn:hover {
                background: #3d8bfe;
            }
            QPushButton#cancel_btn {
                background: #6c757d;
                color: white;
            }
            QPushButton#cancel_btn:hover {
                background: #5a6268;
            }
        """)

        self.save_btn.setObjectName("save_btn")
        self.cancel_btn.setObjectName("cancel_btn")

    def load_data(self):
        """加载设备数据到表单"""
        if not self.device_data:
            return

        self.name_edit.setText(self.device_data.get('sb_name', ''))

        # 加载URLs
        urls = self.device_data.get('sb_urls', [])
        self.url_edit.setPlainText('\n'.join(urls))

        self.need_open_app.setChecked(self.device_data.get('need_open_app', False))
        self.is_clone.setChecked(self.device_data.get('is_clone', False))

        action = self.device_data.get('action', 'do_job')
        index = self.action_combo.findText(action)
        if index >= 0:
            self.action_combo.setCurrentIndex(index)

        self.notes_edit.setPlainText(self.device_data.get('notes', ''))

        # 加载卡账信息
        self.load_card_account_info()

    def get_data(self) -> dict:
        """获取表单数据"""
        urls_text = self.url_edit.toPlainText().strip()
        urls = [url.strip() for url in urls_text.split('\n') if url.strip()]

        return {
            'sb_name': self.name_edit.text().strip(),
            'sb_urls': urls,
            'current_url_index': 0,
            'need_open_app': self.need_open_app.isChecked(),
            'is_clone': self.is_clone.isChecked(),
            'action': self.action_combo.currentText(),
            'notes': self.notes_edit.toPlainText().strip()
        }

    def load_card_account_info(self):
        """加载卡账信息"""
        if not self.device_data:
            self.card_info_text.setPlainText("无设备信息")
            return

        device_name = self.device_data.get('sb_name', '')
        if not device_name:
            self.card_info_text.setPlainText("设备名称为空")
            return

        # 获取主窗口实例
        main_window = self.parent()
        if not hasattr(main_window, 'phone_cards'):
            self.card_info_text.setPlainText("无法获取卡账数据")
            return

        info_text = ""

        # 获取关联的手机卡
        phone_cards = {}
        for card_id, card_data in main_window.phone_cards.items():
            if card_data.get('device_name') == device_name:
                phone_cards[card_id] = card_data

        if phone_cards:
            info_text += "📱 关联手机卡：\n"
            for card_data in phone_cards.values():
                phone_number = card_data.get('phone_number', '未知')
                tags = card_data.get('tags', {})
                tag_text = ', '.join([f"{k}:{v}" for k, v in tags.items()]) if tags else '无标签'
                info_text += f"  📞 {phone_number} ({tag_text})\n"
        else:
            info_text += "📱 关联手机卡：无\n"

        # 获取关联的APP账号
        app_accounts = {}
        for account_id, account_data in main_window.app_accounts.items():
            if account_data.get('device_name') == device_name:
                app_accounts[account_id] = account_data

        if app_accounts:
            info_text += "\n📱 关联APP账号：\n"
            for account_data in app_accounts.values():
                app_name = account_data.get('app_name', '未知')
                phone_number = account_data.get('phone_number', '')
                app_type = account_data.get('app_type', 'original')
                type_text = "原" if app_type == "original" else "分身"
                phone_text = f" [手机号:{phone_number}]" if phone_number else " [无手机号]"
                tags = account_data.get('tags', {})
                tag_text = ', '.join([f"{k}:{v}" for k, v in tags.items()]) if tags else '无标签'
                info_text += f"  📱 {app_name}({type_text}){phone_text} ({tag_text})\n"
        else:
            info_text += "\n📱 关联APP账号：无"

        self.card_info_text.setPlainText(info_text)

    def edit_phone_cards(self):
        """编辑手机卡"""
        device_name = self.device_data.get('sb_name', '') if self.device_data else ''
        if not device_name:
            return

        # 获取主窗口并打开卡账管理
        main_window = self.parent()
        if hasattr(main_window, 'open_card_account_manager'):
            main_window.open_card_account_manager()

            # 使用QTimer延迟设置筛选，确保对话框完全初始化
            from PyQt6.QtCore import QTimer
            def set_filter_delayed():
                # 找到卡账管理对话框并设置筛选
                dialog_found = False
                for widget in QApplication.allWidgets():
                    if isinstance(widget, CardAccountManagerDialog):
                        try:
                            # 切换到手机卡管理标签页并设置设备筛选
                            widget.tab_widget.setCurrentWidget(widget.phone_card_tab)
                            # 确保标签页已经切换完成
                            QTimer.singleShot(100, lambda: widget.phone_card_tab.set_device_filter(device_name))
                            dialog_found = True
                            if hasattr(main_window, 'log'):
                                main_window.log(f"已切换到手机卡管理，筛选设备: {device_name}")
                            break
                        except Exception as e:
                            if hasattr(main_window, 'log'):
                                main_window.log(f"设置手机卡筛选失败: {str(e)}")

                if not dialog_found and hasattr(main_window, 'log'):
                    main_window.log("未找到卡账管理对话框")

            # 延迟200ms执行，确保对话框完全加载
            QTimer.singleShot(200, set_filter_delayed)

    def edit_app_accounts(self):
        """编辑APP账号"""
        device_name = self.device_data.get('sb_name', '') if self.device_data else ''
        if not device_name:
            return

        # 获取主窗口并打开卡账管理
        main_window = self.parent()
        if hasattr(main_window, 'open_card_account_manager'):
            main_window.open_card_account_manager()

            # 使用QTimer延迟设置筛选，确保对话框完全初始化
            from PyQt6.QtCore import QTimer
            def set_filter_delayed():
                # 找到卡账管理对话框并设置筛选
                dialog_found = False
                for widget in QApplication.allWidgets():
                    if isinstance(widget, CardAccountManagerDialog):
                        try:
                            # 切换到APP账号管理标签页并设置设备筛选
                            widget.tab_widget.setCurrentWidget(widget.app_account_tab)
                            # 确保标签页已经切换完成
                            QTimer.singleShot(100, lambda: widget.app_account_tab.set_device_filter(device_name))
                            dialog_found = True
                            if hasattr(main_window, 'log'):
                                main_window.log(f"已切换到APP账号管理，筛选设备: {device_name}")
                            break
                        except Exception as e:
                            if hasattr(main_window, 'log'):
                                main_window.log(f"设置APP账号筛选失败: {str(e)}")

                if not dialog_found and hasattr(main_window, 'log'):
                    main_window.log("未找到卡账管理对话框")

            # 延迟200ms执行，确保对话框完全加载
            QTimer.singleShot(200, set_filter_delayed)


class DeviceManager(QMainWindow):
    """设备管理主窗口"""

    def __init__(self):
        super().__init__()
        self.devices = []
        self.selected_devices = set()
        self.device_cards = {}
        self.running_processes = {}
        self.device_order = []  # 设备显示顺序
        self.device_adb_status = {}

        # 卡账管理数据
        self.phone_cards = {}  # 手机卡号数据
        self.app_accounts = {}  # APP账号数据

        # 运行任务配置记忆
        self.last_run_config = {
            'need_open_app': False,
            'is_clone': False,
            'action': 'do_job'
        }

        # 执行动作列表管理
        self.action_list = ["do_job", "do_test", "monitor", "open_tmall"]  # 默认动作列表

        # 防休眠功能
        self.prevent_sleep_active = False
        self.sleep_prevention_timer = QTimer()
        self.sleep_prevention_timer.timeout.connect(self._prevent_system_sleep)

        # 配置文件路径
        self.config_file = "device_config.json"

        # 创建日志信号
        self.log_signals = LogSignals()
        self.log_signals.log_message.connect(self.handle_log_message)
        self.log_signals.battery_level_updated.connect(self._update_battery_level)

        # 初始化UI
        self.setup_ui()
        self.setup_menu()
        self.setup_style()

        # 加载配置
        self.load_config()

        # 设置定时器用于状态更新
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_all_device_status)
        self.status_timer.start(5000)  # 每5秒更新一次状态

        # 设置ADB自动刷新定时器
        self.adb_refresh_timer = QTimer()
        self.adb_refresh_timer.timeout.connect(self.refresh_adb_status)

        # 启动ADB自动刷新定时器
        self.start_adb_auto_refresh()

        # 初始化重连状态跟踪
        self._init_reconnect_tracking()

        # 延迟刷新ADB状态
        QTimer.singleShot(2000, self.refresh_adb_status)

    def _prevent_system_sleep(self):
        """防止系统休眠"""
        try:
            import ctypes
            # 在Windows上防止系统休眠
            if os.name == 'nt':
                # ES_CONTINUOUS | ES_SYSTEM_REQUIRED | ES_DISPLAY_REQUIRED
                ctypes.windll.kernel32.SetThreadExecutionState(0x80000000 | 0x00000001 | 0x00000002)
            else:
                # 在Linux/Mac上可以使用其他方法，这里暂时跳过
                pass
        except Exception as e:
            # 如果防休眠失败，记录日志但不影响程序运行
            pass

    def _start_sleep_prevention(self):
        """开始防止休眠"""
        if not self.prevent_sleep_active:
            self.prevent_sleep_active = True
            self.sleep_prevention_timer.start(30000)  # 每30秒执行一次防休眠
            self._prevent_system_sleep()  # 立即执行一次
            self.log("🔒 已启动防休眠保护")

    def _stop_sleep_prevention(self):
        """停止防止休眠"""
        if self.prevent_sleep_active:
            self.prevent_sleep_active = False
            self.sleep_prevention_timer.stop()
            try:
                import ctypes
                if os.name == 'nt':
                    # 恢复系统默认电源管理
                    ctypes.windll.kernel32.SetThreadExecutionState(0x80000000)
            except Exception:
                pass
            self.log("🔓 已停止防休眠保护")

    def start_adb_auto_refresh(self):
        """启动ADB自动刷新定时器"""
        # 获取刷新间隔，默认30秒
        interval = getattr(self, 'adb_refresh_interval', 30) * 1000  # 转换为毫秒

        if hasattr(self, 'adb_refresh_timer'):
            self.adb_refresh_timer.stop()  # 先停止现有定时器
            self.adb_refresh_timer.start(interval)
            self.log(f"🔄 ADB自动刷新已启动，间隔: {interval//1000}秒")

    def stop_adb_auto_refresh(self):
        """停止ADB自动刷新定时器"""
        if hasattr(self, 'adb_refresh_timer'):
            self.adb_refresh_timer.stop()
            self.log("⏹️ ADB自动刷新已停止")

    def update_adb_refresh_interval(self, interval_seconds):
        """更新ADB刷新间隔"""
        self.adb_refresh_interval = interval_seconds
        if hasattr(self, 'adb_refresh_timer') and self.adb_refresh_timer.isActive():
            # 重新启动定时器以应用新间隔
            self.start_adb_auto_refresh()

    def _check_running_devices(self):
        """检查是否有设备在运行，决定是否需要防休眠"""
        has_running_devices = False
        for device_name, process in self.running_processes.items():
            if process.poll() is None:  # 进程仍在运行
                has_running_devices = True
                break

        if has_running_devices and not self.prevent_sleep_active:
            self._start_sleep_prevention()
        elif not has_running_devices and self.prevent_sleep_active:
            self._stop_sleep_prevention()

    def setup_ui(self):
        """设置主界面"""
        self.setWindowTitle("📱 设备管理程序 - PyQt6版")
        # 适配1200*1600分辨率
        self.setMinimumSize(1200, 1600)
        self.resize(1200, 1600)
        # 启动时最大化窗口
        self.showMaximized()

        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)

        # 顶部操作按钮栏
        controls = self.create_controls()
        main_layout.addWidget(controls)

        # 中间分割器（设备区域和日志区域）
        splitter = QSplitter(Qt.Orientation.Vertical)

        # 设备网格区域
        devices_container = self.create_devices_container()
        splitter.addWidget(devices_container)

        # 底部日志区域
        log_container = self.create_log_container()
        splitter.addWidget(log_container)

        # 设置分割比例：设备区域占70%，日志区域占30%
        splitter.setSizes([700, 300])
        splitter.setStretchFactor(0, 7)
        splitter.setStretchFactor(1, 3)

        main_layout.addWidget(splitter)

    def create_devices_container(self) -> QWidget:
        """创建设备容器"""
        container = QFrame()
        container.setFrameStyle(QFrame.Shape.StyledPanel)
        container.setStyleSheet("""
            QFrame {
                background: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
            }
        """)

        layout = QVBoxLayout(container)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(5)

        # 设备区域标题
        title_label = QLabel("设备列表")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                padding: 5px;
            }
        """)
        layout.addWidget(title_label)

        # 设备网格滚动区域 - 只允许垂直滚动
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)  # 禁用水平滚动条
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)    # 需要时显示垂直滚动条
        self.scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
        """)

        self.devices_widget = QWidget()
        self.devices_layout = QGridLayout(self.devices_widget)
        self.devices_layout.setSpacing(10)
        self.devices_layout.setContentsMargins(10, 10, 10, 10)

        self.scroll_area.setWidget(self.devices_widget)
        layout.addWidget(self.scroll_area)

        return container

    def create_log_container(self) -> QWidget:
        """创建日志容器"""
        container = QFrame()
        container.setFrameStyle(QFrame.Shape.StyledPanel)
        container.setStyleSheet("""
            QFrame {
                background: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
            }
        """)

        layout = QVBoxLayout(container)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)

        # 创建支持双击关闭的标签页
        self.log_tabs = ClosableTabWidget()
        self.log_tabs.set_main_window(self)
        self.log_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                border-radius: 4px;
                background: white;
            }
            QTabWidget::tab-bar {
                alignment: left;
            }
            QTabBar::tab {
                background: #f0f0f0;
                border: 1px solid #c0c0c0;
                padding: 6px 12px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background: white;
                border-bottom-color: white;
            }
            QTabBar::tab:hover {
                background: #e0e0e0;
            }
        """)

        # 程序日志标签页
        self.main_log = QTextEdit()
        self.main_log.setReadOnly(True)
        # QTextEdit没有setMaximumBlockCount方法，我们可以通过其他方式限制行数
        self.main_log.setStyleSheet("""
            QTextEdit {
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                background: #f8f8f8;
                border: none;
                color: #333;
            }
        """)
        self.log_tabs.addTab(self.main_log, "程序日志")

        # 设备日志字典
        self.device_logs = {}

        layout.addWidget(self.log_tabs)
        return container



    def create_controls(self) -> QWidget:
        """创建顶部操作按钮栏"""
        controls = QFrame()
        controls.setFixedHeight(50)  # 进一步减小高度
        controls.setStyleSheet("""
            QFrame {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 8px;
                margin: 2px;
            }
        """)

        layout = QHBoxLayout(controls)
        layout.setContentsMargins(10, 5, 10, 5)  # 进一步减小边距
        layout.setSpacing(8)  # 减小间距

        # 创建工具栏菜单
        self.create_toolbar_menu(layout)

        layout.addStretch()

        return controls

    def create_toolbar_menu(self, layout):
        """创建工具栏菜单"""
        # 紧凑的按钮样式，避免重叠和截断
        compact_button_style = """
            QPushButton {
                background: rgba(255, 255, 255, 0.9);
                border: 1px solid rgba(0, 0, 0, 0.1);
                border-radius: 15px;
                color: #333333;
                font-size: 11px;
                font-weight: bold;
                padding: 6px 12px;
                min-width: 80px;
                max-width: 120px;
                min-height: 30px;
                max-height: 35px;
                margin: 1px;
            }
            QPushButton:hover {
                background: rgba(255, 255, 255, 1.0);
                border: 1px solid rgba(0, 0, 0, 0.2);
            }
            QPushButton:pressed {
                background: rgba(240, 240, 240, 1.0);
            }
        """

        # 工具栏操作按钮
        toolbar_buttons = [
            ("🔄 刷新ADB", self.force_refresh_adb_status),
            ("⏹️ 全部停止", self.stop_all_devices),
            ("❌ 关闭所有显示", self.close_all_displays),
            ("🔽 并列窗口", self.open_separate_windows),
        ]

        for text, callback in toolbar_buttons:
            btn = QPushButton(text)
            btn.setStyleSheet(compact_button_style)
            btn.clicked.connect(callback)
            layout.addWidget(btn)



    def search_device_info(self):
        """搜索设备信息"""
        from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLineEdit,
                                   QPushButton, QScrollArea, QLabel, QFrame, QTextEdit)

        dialog = QDialog(self)
        dialog.setWindowTitle("搜索设备信息")
        dialog.setMinimumSize(700, 600)
        dialog.resize(700, 600)
        dialog.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QLineEdit {
                padding: 8px;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #007bff;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton#edit_btn {
                background-color: #28a745;
                padding: 4px 8px;
                font-size: 12px;
                min-width: 50px;
            }
            QPushButton#edit_btn:hover {
                background-color: #218838;
            }
        """)

        layout = QVBoxLayout(dialog)

        # 搜索框
        search_layout = QHBoxLayout()
        search_label = QLabel("搜索:")
        search_label.setStyleSheet("font-weight: bold; color: #333;")

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入设备名称、IP地址或备注信息...")
        self.search_input.textChanged.connect(lambda: self.filter_devices_new(dialog))

        search_btn = QPushButton("搜索")
        search_btn.clicked.connect(lambda: self.filter_devices_new(dialog))

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        search_layout.addWidget(search_btn)

        layout.addLayout(search_layout)

        # 结果列表
        result_label = QLabel("搜索结果:")
        result_label.setStyleSheet("font-weight: bold; color: #333; margin-top: 10px;")
        layout.addWidget(result_label)

        # 创建滚动区域
        self.search_scroll_area = QScrollArea()
        self.search_scroll_area.setWidgetResizable(True)
        self.search_scroll_area.setStyleSheet("""
            QScrollArea {
                border: 1px solid #dee2e6;
                border-radius: 5px;
                background-color: white;
            }
        """)

        self.search_results_widget = QFrame()
        self.search_results_layout = QVBoxLayout(self.search_results_widget)
        self.search_results_layout.setSpacing(10)
        self.search_results_layout.setContentsMargins(10, 10, 10, 10)

        self.search_scroll_area.setWidget(self.search_results_widget)
        layout.addWidget(self.search_scroll_area)

        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(dialog.close)
        layout.addWidget(close_btn)

        # 初始显示所有设备
        self.filter_devices_new(dialog)

        dialog.exec()

    def filter_devices_new(self, dialog):
        """过滤设备列表 - 新版本"""
        if not hasattr(self, 'search_results_layout'):
            return

        search_text = self.search_input.text().lower() if hasattr(self, 'search_input') else ""

        # 清除现有结果
        for i in reversed(range(self.search_results_layout.count())):
            child = self.search_results_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        matched_devices = []
        for device in self.devices:
            device_name = device.get('sb_name', '').lower()
            device_urls = ' '.join(device.get('sb_urls', [])).lower()
            device_notes = device.get('notes', '').lower()

            # 获取设备关联的卡账信息
            phone_cards_text = ""
            app_accounts_text = ""

            if device.get('sb_name'):
                # 手机卡信息
                phone_cards = self.get_device_phone_cards(device.get('sb_name'))
                for card_data in phone_cards.values():
                    phone_cards_text += f" {card_data.get('phone_number', '')}"
                    tags = card_data.get('tags', {})
                    for tag_name, tag_value in tags.items():
                        phone_cards_text += f" {tag_name} {tag_value}"

                # APP账号信息
                app_accounts = self.get_device_app_accounts(device.get('sb_name'))
                for account_data in app_accounts.values():
                    app_accounts_text += f" {account_data.get('app_name', '')}"
                    app_accounts_text += f" {account_data.get('phone_number', '')}"
                    tags = account_data.get('tags', {})
                    for tag_name, tag_value in tags.items():
                        app_accounts_text += f" {tag_name} {tag_value}"

            phone_cards_text = phone_cards_text.lower()
            app_accounts_text = app_accounts_text.lower()

            # 检查是否匹配搜索条件（包括卡账信息）
            if (search_text in device_name or
                search_text in device_urls or
                search_text in device_notes or
                search_text in phone_cards_text or
                search_text in app_accounts_text or
                not search_text):
                matched_devices.append(device)

        # 创建搜索结果项
        for device in matched_devices:
            result_item = self.create_search_result_item(device, dialog)
            self.search_results_layout.addWidget(result_item)

        # 添加弹性空间
        self.search_results_layout.addStretch()

    def create_search_result_item(self, device, parent_dialog):
        """创建搜索结果项"""
        from PyQt6.QtWidgets import QFrame, QVBoxLayout, QHBoxLayout, QPushButton, QTextEdit, QLabel

        item_frame = QFrame()
        item_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                margin: 2px;
            }
            QFrame:hover {
                border-color: #007bff;
                background-color: #f8f9ff;
            }
        """)

        layout = QVBoxLayout(item_frame)
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(8)

        # 顶部：设备名称和编辑按钮
        top_layout = QHBoxLayout()

        device_name_label = QLabel()
        device_name_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #333;")

        # 设置设备名称，如果有搜索关键词则高亮显示
        device_name = device.get('sb_name', '未知')
        search_text = self.search_input.text().strip() if hasattr(self, 'search_input') else ""
        if search_text and search_text.lower() in device_name.lower():
            highlighted_name = self.highlight_search_terms(f"设备: {device_name}", search_text)
            device_name_label.setText(highlighted_name)
        else:
            device_name_label.setText(f"设备: {device_name}")

        top_layout.addWidget(device_name_label)

        top_layout.addStretch()

        edit_btn = QPushButton("编辑")
        edit_btn.setObjectName("edit_btn")
        edit_btn.clicked.connect(lambda: self.edit_device_from_search(device, parent_dialog))
        top_layout.addWidget(edit_btn)

        layout.addLayout(top_layout)

        # 设备信息文本区域（可选择和复制）
        info_text = QTextEdit()
        info_text.setReadOnly(True)
        info_text.setMaximumHeight(120)
        info_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                background-color: #f8f9fa;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                color: #333;
                padding: 8px;
            }
            QTextEdit::selection {
                background-color: #007bff;
                color: white;
            }
        """)

        # 构建显示文本
        display_text = f"连接地址: {', '.join(device.get('sb_urls', ['无']))}\n"
        display_text += f"备注: {device.get('notes', '无')}\n"

        # 添加状态信息
        adb_status = "已连接" if device.get('adb_connected', False) else "未连接"
        display_status = "运行中" if device.get('display_active', False) else "未运行"
        task_status = "执行中" if device.get('task_running', False) else "空闲"

        display_text += f"ADB状态: {adb_status} | 显示状态: {display_status} | 任务状态: {task_status}\n"

        # 添加卡账信息
        device_name = device.get('sb_name', '')
        if device_name:
            # 手机卡信息
            phone_cards = self.get_device_phone_cards(device_name)
            if phone_cards:
                display_text += "\n📱 关联手机卡: "
                card_info = []
                for card_data in phone_cards.values():
                    phone_number = card_data.get('phone_number', '未知')
                    tags = card_data.get('tags', {})
                    tag_text = ', '.join([f"{k}:{v}" for k, v in tags.items()]) if tags else '无标签'
                    card_info.append(f"{phone_number}({tag_text})")
                display_text += "; ".join(card_info)
            else:
                display_text += "\n📱 关联手机卡: 无"

            # APP账号信息
            app_accounts = self.get_device_app_accounts(device_name)
            if app_accounts:
                display_text += "\n📱 关联APP账号: "
                app_info = []
                for account_data in app_accounts.values():
                    app_name = account_data.get('app_name', '未知')
                    phone_number = account_data.get('phone_number', '')
                    phone_text = f"手机号:{phone_number}" if phone_number else "无手机号"
                    app_type = account_data.get('app_type', 'original')
                    type_text = "原应用" if app_type == "original" else "分身应用"
                    tags = account_data.get('tags', {})
                    tag_text = ', '.join([f"{k}:{v}" for k, v in tags.items()]) if tags else '无标签'
                    app_info.append(f"{app_name}({phone_text}, {type_text}, {tag_text})")
                display_text += "; ".join(app_info)
            else:
                display_text += "\n📱 关联APP账号: 无"

        # 设置文本并高亮搜索关键词
        self.set_highlighted_text(info_text, display_text)
        layout.addWidget(info_text)

        return item_frame

    def set_highlighted_text(self, text_edit, text):
        """设置高亮文本"""
        # 获取搜索关键词
        search_text = self.search_input.text().strip() if hasattr(self, 'search_input') else ""

        if not search_text:
            # 如果没有搜索关键词，直接设置普通文本
            text_edit.setPlainText(text)
            return

        # 设置HTML格式的文本以支持高亮
        text_edit.setHtml(self.highlight_search_terms(text, search_text))

    def highlight_search_terms(self, text, search_term):
        """在文本中高亮搜索关键词"""
        import re

        if not search_term:
            return text

        # 转义HTML特殊字符
        text = text.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')

        # 使用正则表达式进行不区分大小写的搜索和替换
        pattern = re.compile(re.escape(search_term), re.IGNORECASE)

        def replace_func(match):
            return f'<span style="background-color: #ffeb3b; color: #333; font-weight: bold; padding: 1px 2px; border-radius: 2px;">{match.group()}</span>'

        highlighted_text = pattern.sub(replace_func, text)

        # 将换行符转换为HTML换行
        highlighted_text = highlighted_text.replace('\n', '<br>')

        return highlighted_text

    def edit_device_from_search(self, device, parent_dialog):
        """从搜索结果编辑设备"""
        dialog = DeviceEditDialog(device, parent_dialog)

        # 连接保存信号
        def on_save():
            updated_data = dialog.get_data()
            if updated_data['sb_name']:
                # 检查设备名称是否重复（排除自己）
                if any(d['sb_name'] == updated_data['sb_name'] and d != device for d in self.devices):
                    QMessageBox.warning(dialog, "警告", "设备名称已存在，请使用不同的名称")
                    return

                # 找到并更新设备
                for i, d in enumerate(self.devices):
                    if d.get('sb_name') == device.get('sb_name'):
                        self.devices[i].update(updated_data)
                        break

                # 保存配置
                self.save_config()

                # 重新渲染设备列表
                self.render_devices()

                # 刷新搜索结果
                self.filter_devices_new(parent_dialog)

                self.log(f"设备 {updated_data.get('sb_name', '未知')} 配置已更新")
                dialog.close()  # 保存成功后关闭对话框
            else:
                QMessageBox.warning(dialog, "警告", "设备名称不能为空！")

        # 连接按钮信号
        dialog.save_btn.clicked.disconnect()  # 断开原有连接
        dialog.save_btn.clicked.connect(on_save)

        # 显示非模态对话框
        dialog.show()

    def open_settings(self):
        """打开设置对话框"""
        from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout,
                                   QSpinBox, QCheckBox, QPushButton, QTabWidget,
                                   QWidget, QFormLayout)

        dialog = QDialog(self)
        dialog.setWindowTitle("设置")
        dialog.setFixedSize(500, 400)
        dialog.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #e9ecef;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #007bff;
            }
            QLabel {
                color: #333;
            }
            QSpinBox, QLineEdit, QTextEdit {
                padding: 5px;
                border: 1px solid #dee2e6;
                border-radius: 3px;
            }
            QCheckBox {
                color: #333;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)

        layout = QVBoxLayout(dialog)

        # 创建选项卡
        tab_widget = QTabWidget()

        # 常规设置选项卡
        general_tab = QWidget()
        general_layout = QFormLayout(general_tab)

        # ADB刷新间隔
        self.adb_interval_spin = QSpinBox()
        self.adb_interval_spin.setRange(5, 300)
        self.adb_interval_spin.setValue(getattr(self, 'adb_refresh_interval', 30))
        self.adb_interval_spin.setSuffix(" 秒")
        general_layout.addRow("ADB刷新间隔:", self.adb_interval_spin)

        # 自动连接设备
        self.auto_connect_check = QCheckBox("启动时自动连接设备")
        self.auto_connect_check.setChecked(getattr(self, 'auto_connect_on_startup', True))
        general_layout.addRow(self.auto_connect_check)

        # 最小化到系统托盘
        self.minimize_to_tray_check = QCheckBox("最小化到系统托盘")
        self.minimize_to_tray_check.setChecked(getattr(self, 'minimize_to_tray', False))
        general_layout.addRow(self.minimize_to_tray_check)

        tab_widget.addTab(general_tab, "常规")

        # 显示设置选项卡
        display_tab = QWidget()
        display_layout = QFormLayout(display_tab)

        # 默认窗口大小
        self.default_width_spin = QSpinBox()
        self.default_width_spin.setRange(800, 2000)
        self.default_width_spin.setValue(getattr(self, 'default_window_width', 1200))
        display_layout.addRow("默认窗口宽度:", self.default_width_spin)

        self.default_height_spin = QSpinBox()
        self.default_height_spin.setRange(600, 1500)
        self.default_height_spin.setValue(getattr(self, 'default_window_height', 800))
        display_layout.addRow("默认窗口高度:", self.default_height_spin)

        # 设备卡片列数
        self.max_cols_spin = QSpinBox()
        self.max_cols_spin.setRange(1, 10)
        self.max_cols_spin.setValue(getattr(self, 'max_device_cols', 6))
        display_layout.addRow("最大列数:", self.max_cols_spin)

        tab_widget.addTab(display_tab, "显示")

        layout.addWidget(tab_widget)

        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(dialog.reject)

        save_btn = QPushButton("保存")
        save_btn.clicked.connect(lambda: self.save_settings(dialog))
        save_btn.setDefault(True)

        button_layout.addWidget(cancel_btn)
        button_layout.addWidget(save_btn)

        layout.addLayout(button_layout)

        dialog.exec()

    def save_settings(self, dialog):
        """保存设置"""
        try:
            # 保存设置到实例变量
            old_interval = getattr(self, 'adb_refresh_interval', 30)
            new_interval = self.adb_interval_spin.value()

            self.adb_refresh_interval = new_interval
            self.auto_connect_on_startup = self.auto_connect_check.isChecked()
            self.minimize_to_tray = self.minimize_to_tray_check.isChecked()
            self.default_window_width = self.default_width_spin.value()
            self.default_window_height = self.default_height_spin.value()
            self.max_device_cols = self.max_cols_spin.value()

            # 如果ADB刷新间隔发生变化，更新定时器
            if old_interval != new_interval:
                self.update_adb_refresh_interval(new_interval)

            # 保存到配置文件
            self.save_config()
            self.log("设置已保存")
            dialog.accept()

        except Exception as e:
            self.log(f"保存设置失败: {str(e)}")

    def open_card_account_manager(self):
        """打开卡账管理界面"""
        try:
            dialog = CardAccountManagerDialog(self)
            dialog.show()
        except Exception as e:
            self.log(f"打开卡账管理界面失败: {str(e)}")

    def open_action_maintenance(self):
        """打开执行动作维护对话框"""
        try:
            dialog = ActionMaintenanceDialog(self)
            dialog.exec()
        except Exception as e:
            self.log(f"打开执行动作维护对话框失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"打开执行动作维护对话框失败:\n{str(e)}")

    def refresh_action_combos(self):
        """刷新所有对话框中的动作下拉框"""
        # 这个方法可以在将来用于实时更新已打开的对话框
        pass

    def setup_menu(self):
        """设置菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu("文件")

        # 导入配置
        import_action = QAction("导入配置", self)
        import_action.triggered.connect(self.import_config)
        file_menu.addAction(import_action)

        # 导出配置
        export_action = QAction("导出配置", self)
        export_action.triggered.connect(self.export_config)
        file_menu.addAction(export_action)

        file_menu.addSeparator()

        # 退出
        exit_action = QAction("退出", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 设备菜单
        device_menu = menubar.addMenu("设备")

        # 添加设备
        add_device_action = QAction("➕ 添加设备", self)
        add_device_action.setShortcut("Ctrl+N")
        add_device_action.triggered.connect(self.add_device)
        device_menu.addAction(add_device_action)

        # 搜索设备信息
        search_device_action = QAction("🔍 搜索信息", self)
        search_device_action.setShortcut("Ctrl+F")
        search_device_action.triggered.connect(self.search_device_info)
        device_menu.addAction(search_device_action)

        # 卡账管理
        card_account_action = QAction("📱 卡账管理", self)
        card_account_action.setShortcut("Ctrl+M")
        card_account_action.triggered.connect(self.open_card_account_manager)
        device_menu.addAction(card_account_action)

        device_menu.addSeparator()

        # 全选
        select_all_action = QAction("全选", self)
        select_all_action.setShortcut("Ctrl+A")
        select_all_action.triggered.connect(self.select_all_devices)
        device_menu.addAction(select_all_action)

        # 取消选择
        deselect_all_action = QAction("取消选择", self)
        deselect_all_action.setShortcut("Ctrl+D")
        deselect_all_action.triggered.connect(self.deselect_all_devices)
        device_menu.addAction(deselect_all_action)

        # 设置菜单
        settings_menu = menubar.addMenu("设置")

        # 程序设置
        settings_action = QAction("⚙️ 程序设置", self)
        settings_action.setShortcut("Ctrl+,")
        settings_action.triggered.connect(self.open_settings)
        settings_menu.addAction(settings_action)

        # 执行动作维护
        action_maintenance_action = QAction("🔧 执行动作维护", self)
        action_maintenance_action.triggered.connect(self.open_action_maintenance)
        settings_menu.addAction(action_maintenance_action)

        # 帮助菜单
        help_menu = menubar.addMenu("帮助")

        # 关于
        about_action = QAction("关于", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_style(self):
        """设置全局样式"""
        self.setStyleSheet("""
            QMainWindow {
                background: #f5f5f5;
            }

            QScrollArea {
                border: none;
                background: white;
            }

            QPushButton {
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
                min-width: 100px;
            }

            QPushButton#btn_primary {
                background: #4facfe;
                color: white;
            }
            QPushButton#btn_primary:hover {
                background: #3d8bfe;
            }

            QPushButton#btn_success {
                background: #4CAF50;
                color: white;
            }
            QPushButton#btn_success:hover {
                background: #45a049;
            }

            QPushButton#btn_danger {
                background: #f44336;
                color: white;
            }
            QPushButton#btn_danger:hover {
                background: #da190b;
            }

            QPushButton#btn_secondary {
                background: #6c757d;
                color: white;
            }
            QPushButton#btn_secondary:hover {
                background: #5a6268;
            }

            QMenuBar {
                background: white;
                border-bottom: 1px solid #e9ecef;
                padding: 4px;
            }

            QMenuBar::item {
                padding: 8px 12px;
                background: transparent;
            }

            QMenuBar::item:selected {
                background: #f8f9fa;
                border-radius: 4px;
            }

            QMenu {
                background: white;
                border: 1px solid #ddd;
                border-radius: 6px;
                padding: 4px;
            }

            QMenu::item {
                padding: 8px 16px;
                border-radius: 4px;
            }

            QMenu::item:selected {
                background: #f8f9fa;
            }
        """)

    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.devices = data.get('devices', [])
                    self.device_order = data.get('device_order', [])
                    self.phone_cards = data.get('phone_cards', {})
                    self.app_accounts = data.get('app_accounts', {})
                    self.action_list = data.get('action_list', ["do_job", "do_test", "monitor", "open_tmall"])

                    # 加载设置
                    settings = data.get('settings', {})
                    self.adb_refresh_interval = settings.get('adb_refresh_interval', 30)
                    self.auto_connect_on_startup = settings.get('auto_connect_on_startup', True)
                    self.minimize_to_tray = settings.get('minimize_to_tray', False)
                    self.default_window_width = settings.get('default_window_width', 1200)
                    self.default_window_height = settings.get('default_window_height', 800)
                    self.max_device_cols = settings.get('max_device_cols', 6)

                    self.convert_legacy_data()
            else:
                self.create_sample_devices()
                # 设置默认值
                self.adb_refresh_interval = 30
                self.auto_connect_on_startup = True
                self.minimize_to_tray = False
                self.default_window_width = 1200
                self.default_window_height = 800
                self.max_device_cols = 6

            # 初始化设备顺序（如果为空）
            self.initialize_device_order()

            self.render_devices()
            self.log("配置加载完成")

        except Exception as e:
            self.log(f"加载配置失败: {str(e)}")
            self.devices = []
            self.phone_cards = {}
            self.app_accounts = {}
            # 设置默认值
            self.adb_refresh_interval = 30
            self.auto_connect_on_startup = True
            self.minimize_to_tray = False
            self.default_window_width = 1200
            self.default_window_height = 800
            self.max_device_cols = 6

    # 卡账管理辅助方法
    def generate_card_id(self):
        """生成手机卡ID"""
        import uuid
        return str(uuid.uuid4())

    def generate_account_id(self):
        """生成APP账号ID"""
        import uuid
        return str(uuid.uuid4())

    def get_device_phone_cards(self, device_name):
        """获取设备关联的手机卡"""
        return {card_id: card_data for card_id, card_data in self.phone_cards.items()
                if card_data.get('device_name') == device_name}

    def get_device_app_accounts(self, device_name):
        """获取设备关联的APP账号"""
        return {account_id: account_data for account_id, account_data in self.app_accounts.items()
                if account_data.get('device_name') == device_name}

    def get_device_names_list(self):
        """获取所有设备名称列表"""
        return [device.get('sb_name', '') for device in self.devices if device.get('sb_name')]

    def create_sample_devices(self):
        """创建示例设备"""
        self.devices = [
            {
                'sb_name': '示例设备1',
                'sb_urls': ['192.168.1.100:5555'],
                'current_url_index': 0,
                'need_open_app': False,
                'is_clone': False,
                'action': 'do_job',
                'notes': '这是示例设备的备注信息'
            },
            {
                'sb_name': '示例设备2',
                'sb_urls': ['192.168.1.101:5555'],
                'current_url_index': 0,
                'need_open_app': True,
                'is_clone': False,
                'action': 'open_tmall',
                'notes': '用于测试天猫功能的设备'
            }
        ]
        self.save_config()

    def convert_legacy_data(self):
        """转换旧版本数据格式"""
        converted_count = 0
        for device in self.devices:
            if 'sb_url' in device and 'sb_urls' not in device:
                device['sb_urls'] = [device['sb_url']]
                device['current_url_index'] = 0
                del device['sb_url']
                converted_count += 1

            if 'sb_urls' in device:
                if 'current_url_index' not in device:
                    device['current_url_index'] = 0
                if device['current_url_index'] >= len(device['sb_urls']):
                    device['current_url_index'] = 0

            if 'notes' not in device:
                device['notes'] = ''
                converted_count += 1

        if converted_count > 0:
            self.log(f"已转换 {converted_count} 个设备为新格式")
            self.save_config()

    def save_config(self):
        """保存配置文件"""
        try:
            config_data = {
                'devices': self.devices,
                'device_order': self.device_order,
                'phone_cards': self.phone_cards,
                'app_accounts': self.app_accounts,
                'action_list': self.action_list,
                'settings': {
                    'adb_refresh_interval': getattr(self, 'adb_refresh_interval', 30),
                    'auto_connect_on_startup': getattr(self, 'auto_connect_on_startup', True),
                    'minimize_to_tray': getattr(self, 'minimize_to_tray', False),
                    'default_window_width': getattr(self, 'default_window_width', 1200),
                    'default_window_height': getattr(self, 'default_window_height', 800),
                    'max_device_cols': getattr(self, 'max_device_cols', 6)
                }
            }
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=4)
            self.log("配置保存成功")
            return True
        except Exception as e:
            self.log(f"保存配置失败: {str(e)}")
            return False

    def render_devices(self):
        """渲染设备网格"""
        try:
            # 防止重复渲染
            if hasattr(self, '_rendering') and self._rendering:
                return
            self._rendering = True

            # 清除现有的设备卡片
            try:
                for card in list(self.device_cards.values()):
                    if card and card.parent():
                        card.setParent(None)
                        card.deleteLater()
                self.device_cards.clear()
            except Exception as e:
                self.log(f"清除设备卡片时出错: {str(e)}")

            if not self.devices:
                # 显示空状态
                empty_label = QLabel("暂无设备\n点击\"添加设备\"按钮开始添加您的第一个设备")
                empty_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                empty_label.setStyleSheet("""
                    QLabel {
                        color: #666;
                        font-size: 16px;
                        padding: 60px;
                    }
                """)
                self.devices_layout.addWidget(empty_label, 0, 0, 1, -1)
                return
        except Exception as e:
            self.log(f"设备渲染初始化失败: {str(e)}")
            return
        finally:
            self._rendering = False

        # 计算可用宽度和高度 - 自适应布局
        scroll_width = self.scroll_area.viewport().width() if hasattr(self, 'scroll_area') else 1200
        scroll_height = self.scroll_area.viewport().height() if hasattr(self, 'scroll_area') else 800

        card_min_width = 180  # 卡片最小宽度（包括边距）
        margin = 20  # 左右边距
        spacing = 10  # 卡片间距

        # 计算每行可以放置的卡片数量
        available_width = scroll_width - margin
        max_cols = max(1, (available_width + spacing) // (card_min_width + spacing))

        # 计算卡片高度 - 固定显示3行，平分可用高度
        visible_rows = 3  # 固定显示3行
        available_height = scroll_height - margin
        card_height = max(120, (available_height - (visible_rows - 1) * spacing) // visible_rows)  # 最小高度120px

        # 根据自定义顺序排序设备
        ordered_devices = self.get_ordered_devices()

        # 添加设备卡片
        for i, device in enumerate(ordered_devices):
            try:
                # 添加运行时状态
                device.setdefault('adb_connected', False)
                device.setdefault('display_active', False)
                device.setdefault('task_running', False)

                # 创建设备卡片，添加异常处理
                card = DeviceCard(device, card_height=card_height)

                # 连接信号，添加异常处理
                try:
                    card.clicked.connect(self.on_device_clicked)
                    card.doubleClicked.connect(self.on_device_double_clicked)
                    card.rightClicked.connect(self.on_device_right_clicked)
                    card.deviceReordered.connect(self.handle_device_reorder)
                except Exception as e:
                    self.log(f"连接设备卡片信号失败: {str(e)}")

                row = i // max_cols
                col = i % max_cols
                self.devices_layout.addWidget(card, row, col)

                self.device_cards[device['sb_name']] = card

            except Exception as e:
                self.log(f"创建设备卡片失败 {device.get('sb_name', '未知')}: {str(e)}")
                continue

        # 设置列的拉伸因子，确保卡片平分宽度
        for c in range(max_cols):
            self.devices_layout.setColumnStretch(c, 1)

        # 清除多余的列拉伸
        for c in range(max_cols, 20):  # 清除最多20列的拉伸设置
            self.devices_layout.setColumnStretch(c, 0)

        # 设置行的拉伸因子
        total_rows = (len(self.devices) - 1) // max_cols + 1 if self.devices else 0
        for r in range(total_rows):
            self.devices_layout.setRowStretch(r, 0)

        # 添加底部弹性空间
        if total_rows > 0:
            self.devices_layout.setRowStretch(total_rows, 1)

        # 渲染完成后更新选择状态和设备状态UI
        self.update_selection_display()
        # 只更新UI，不检查显示状态（提高渲染性能）
        self.update_all_device_status(force_display_check=False)

    def resizeEvent(self, event):
        """窗口大小改变时重新布局设备"""
        try:
            super().resizeEvent(event)
            # 延迟重新布局，避免频繁调用
            if hasattr(self, 'resize_timer'):
                self.resize_timer.stop()
            else:
                from PyQt6.QtCore import QTimer
                self.resize_timer = QTimer()
                self.resize_timer.setSingleShot(True)
                self.resize_timer.timeout.connect(self.safe_render_devices)

            self.resize_timer.start(200)  # 增加延迟到200ms，减少频繁调用
        except Exception as e:
            self.log(f"窗口大小调整事件处理失败: {str(e)}")

    def safe_render_devices(self):
        """安全的设备渲染方法，带异常处理"""
        try:
            self.render_devices()
        except Exception as e:
            self.log(f"设备渲染失败: {str(e)}")
            # 尝试恢复基本布局
            try:
                self.render_devices_fallback()
            except Exception as e2:
                self.log(f"设备渲染恢复失败: {str(e2)}")

    def render_devices_fallback(self):
        """设备渲染失败时的备用方法"""
        # 清除现有布局
        for i in reversed(range(self.devices_layout.count())):
            item = self.devices_layout.itemAt(i)
            if item:
                widget = item.widget()
                if widget:
                    self.devices_layout.removeWidget(widget)
                    widget.setParent(None)

        # 显示错误信息
        error_label = QLabel("设备布局出现问题，请重启程序")
        error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        error_label.setStyleSheet("color: red; font-size: 14px; padding: 20px;")
        self.devices_layout.addWidget(error_label, 0, 0)

    def initialize_device_order(self):
        """初始化设备顺序"""
        if not self.device_order:
            # 如果没有保存的顺序，使用当前设备列表的顺序
            self.device_order = [device.get('sb_name', '') for device in self.devices]
        else:
            # 检查是否有新设备需要添加到顺序中
            current_devices = {device.get('sb_name', '') for device in self.devices}
            ordered_devices = set(self.device_order)

            # 添加新设备到顺序末尾
            new_devices = current_devices - ordered_devices
            self.device_order.extend(list(new_devices))

            # 移除已删除的设备
            self.device_order = [name for name in self.device_order if name in current_devices]

    def get_ordered_devices(self):
        """根据自定义顺序获取设备列表"""
        if not self.device_order:
            return self.devices

        # 创建设备名称到设备对象的映射
        device_map = {device.get('sb_name', ''): device for device in self.devices}

        # 按照自定义顺序排列设备
        ordered_devices = []
        for device_name in self.device_order:
            if device_name in device_map:
                ordered_devices.append(device_map[device_name])

        # 添加不在顺序中的设备（新设备）
        ordered_names = set(self.device_order)
        for device in self.devices:
            device_name = device.get('sb_name', '')
            if device_name not in ordered_names:
                ordered_devices.append(device)

        return ordered_devices

    def handle_device_reorder(self, source_device: str, target_device: str):
        """处理设备重新排序"""
        if source_device == target_device:
            return

        try:
            # 确保设备顺序列表是最新的
            self.initialize_device_order()

            # 记录原始顺序用于调试
            original_order = self.device_order.copy()

            # 获取源设备和目标设备的原始位置
            source_index = self.device_order.index(source_device) if source_device in self.device_order else -1
            target_index = self.device_order.index(target_device) if target_device in self.device_order else -1

            if source_index == -1 or target_index == -1:
                self.log(f"设备排序失败：源设备或目标设备不存在")
                return

            # 判断插入位置的逻辑
            # 如果源设备在目标设备的右边（索引更大），插入到目标设备前面
            # 如果源设备在目标设备的左边（索引更小），插入到目标设备后面
            insert_after = source_index < target_index

            # 移除源设备
            self.device_order.remove(source_device)

            # 重新获取目标设备位置（因为移除源设备后位置可能变化）
            target_index = self.device_order.index(target_device)

            # 根据原始位置关系决定插入位置
            if insert_after:
                # 源设备原本在左边，插入到目标设备后面
                insert_position = target_index + 1
                position_desc = "后面"
            else:
                # 源设备原本在右边，插入到目标设备前面
                insert_position = target_index
                position_desc = "前面"

            self.device_order.insert(insert_position, source_device)

            # 保存配置并重新渲染
            self.save_config()
            self.render_devices()

            self.log(f"设备排序：'{source_device}' 移动到 '{target_device}' {position_desc}")
            self.log(f"原始顺序：{original_order}")
            self.log(f"新的顺序：{self.device_order}")

        except Exception as e:
            self.log(f"设备排序失败: {str(e)}")

    def log(self, message: str, device_name: str = None):
        """记录日志"""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        print(log_message)

        # 如果指定了设备名称，只添加到设备日志，不添加到程序日志
        if device_name and hasattr(self, 'device_logs'):
            self.add_device_log(device_name, message)
        else:
            # 只有非设备相关的日志才添加到主日志
            if hasattr(self, 'main_log'):
                self.main_log.append(log_message)
                # 自动滚动到底部
                cursor = self.main_log.textCursor()
                cursor.movePosition(cursor.MoveOperation.End)
                self.main_log.setTextCursor(cursor)

    def handle_log_message(self, message: str, device_name: str):
        """处理来自子线程的日志消息（线程安全）"""
        if device_name:
            self.log(message, device_name)
        else:
            self.log(message)

    def log_from_thread(self, message: str, device_name: str = ""):
        """从子线程安全地记录日志"""
        self.log_signals.log_message.emit(message, device_name)

    def add_device_log(self, device_name: str, message: str):
        """为指定设备添加日志"""
        if device_name not in self.device_logs:
            # 创建新的设备日志标签页
            device_log = QTextEdit()
            device_log.setReadOnly(True)
            # QTextEdit没有setMaximumBlockCount方法
            device_log.setStyleSheet("""
                QTextEdit {
                    font-family: 'Consolas', 'Monaco', monospace;
                    font-size: 12px;
                    background: #f8f8f8;
                    border: none;
                    color: #333;
                }
            """)
            self.device_logs[device_name] = device_log
            self.log_tabs.addTab(device_log, device_name)

        # 添加日志消息
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        self.device_logs[device_name].append(log_message)

        # 自动滚动到底部
        cursor = self.device_logs[device_name].textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.device_logs[device_name].setTextCursor(cursor)

    def switch_to_device_log_tab(self, device_name: str):
        """切换到指定设备的日志标签页"""
        if device_name in self.device_logs:
            # 找到对应的标签页索引
            for i in range(self.log_tabs.count()):
                if self.log_tabs.tabText(i) == device_name:
                    self.log_tabs.setCurrentIndex(i)
                    break
        else:
            # 如果设备日志标签页不存在，创建一个空的
            self.add_device_log(device_name, f"设备 {device_name} 日志开始")

    def ensure_device_log_tab(self, device_name: str):
        """确保设备有日志标签页"""
        if device_name not in self.device_logs:
            # 创建设备日志标签页
            self.add_device_log(device_name, f"设备 {device_name} 任务开始")
            self.log(f"为设备 {device_name} 创建日志标签页")







    def stop_all_devices(self):
        """停止所有设备任务"""
        self.log("开始停止所有设备任务...")

        try:
            # 获取所有正在运行的设备任务
            running_devices = list(self.running_processes.keys())

            if not running_devices:
                QMessageBox.information(self, "停止所有任务", "当前没有正在运行的设备任务")
                return

            # 询问用户确认
            device_list = "、".join(running_devices)
            reply = QMessageBox.question(
                self,
                "确认停止",
                f"确定要停止所有 {len(running_devices)} 个设备的运行任务吗？\n\n"
                f"运行中的设备：{device_list}\n\n"
                f"注意：这只会停止设备任务，不会关闭scrcpy显示和adb连接",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply != QMessageBox.StandardButton.Yes:
                self.log("用户取消停止所有设备任务")
                return

            # 停止所有设备任务
            success_count = 0
            failed_devices = []

            # 创建运行设备的副本，避免在迭代时修改字典
            devices_to_stop = running_devices.copy()

            for device_name in devices_to_stop:
                try:
                    self.log(f"正在停止设备 {device_name} 的任务...")
                    self.stop_device(device_name)
                    success_count += 1
                    self.log(f"✓ 设备 {device_name} 任务已停止")
                except Exception as e:
                    self.log(f"✗ 停止设备 {device_name} 任务失败: {str(e)}")
                    failed_devices.append(device_name)

            # 更新设备卡片显示
            self.update_device_status()

            # 显示结果
            self.log(f"停止所有设备任务完成：成功停止 {success_count}/{len(devices_to_stop)} 个设备")

            if success_count == len(devices_to_stop):
                QMessageBox.information(self, "停止完成",
                                      f"成功停止所有 {success_count} 个设备的运行任务")
            else:
                failed_list = "、".join(failed_devices)
                QMessageBox.warning(self, "部分停止失败",
                                  f"成功停止 {success_count} 个设备任务\n"
                                  f"停止失败：{failed_list}")

        except Exception as e:
            self.log(f"停止所有设备任务失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"停止所有设备任务时发生错误：{str(e)}")

    def close_all_displays(self):
        """关闭所有显示"""
        if not WIN_API_AVAILABLE:
            QMessageBox.warning(self, "功能不可用", "Windows API不可用，无法使用关闭所有显示功能")
            return

        self.log("开始关闭所有显示...")

        try:
            # 获取所有scrcpy窗口（详细日志模式）
            scrcpy_windows = self.get_all_scrcpy_windows(verbose=True)

            if not scrcpy_windows:
                QMessageBox.information(self, "关闭所有显示", "没有找到已打开的设备显示窗口")
                return

            # 询问用户确认
            reply = QMessageBox.question(
                self,
                "确认关闭",
                f"确定要关闭所有 {len(scrcpy_windows)} 个设备显示窗口吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply != QMessageBox.StandardButton.Yes:
                self.log("用户取消关闭所有显示")
                return

            # 关闭所有窗口
            success_count = 0
            failed_windows = []

            for window_info in scrcpy_windows:
                if self.close_window(window_info):
                    success_count += 1
                else:
                    failed_windows.append(window_info['title'])

            # 无需清理内部记录，使用实时检测

            # 显示结果
            self.log(f"关闭所有显示完成：成功关闭 {success_count}/{len(scrcpy_windows)} 个窗口")

            if success_count == len(scrcpy_windows):
                QMessageBox.information(self, "关闭完成", f"成功关闭所有 {success_count} 个设备显示窗口")
            else:
                failed_list = "、".join(failed_windows)
                QMessageBox.warning(self, "部分关闭失败",
                                  f"成功关闭 {success_count} 个窗口\n"
                                  f"关闭失败：{failed_list}")

        except Exception as e:
            self.log(f"关闭所有显示失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"关闭所有显示时发生错误：{str(e)}")

    def has_display_running(self, device_name: str, scrcpy_windows: list = None) -> bool:
        """检查设备是否有显示正在运行 - 支持批量查询优化

        Args:
            device_name: 设备名称
            scrcpy_windows: 可选的窗口列表，如果提供则不重新查询
        """
        if not WIN_API_AVAILABLE:
            return False

        try:
            # 如果没有提供窗口列表，则查询一次
            if scrcpy_windows is None:
                scrcpy_windows = self.get_all_scrcpy_windows()

            return any(device_name in window['title'] for window in scrcpy_windows)
        except Exception as e:
            self.log(f"检查设备 {device_name} 显示状态失败: {str(e)}")
            return False

    def close_window(self, window_info):
        """关闭单个窗口"""
        try:
            import ctypes
            user32 = ctypes.windll.user32

            hwnd = window_info['hwnd']
            window_title = window_info['title']

            # 验证窗口句柄是否有效
            if not user32.IsWindow(hwnd):
                self.log(f"窗口 '{window_title}' 句柄无效，可能已关闭")
                return True

            self.log(f"关闭窗口: '{window_title}' (HWND: {hwnd})")

            # 方法1: 发送关闭消息 (WM_CLOSE = 0x0010)
            WM_CLOSE = 0x0010
            result = user32.SendMessageW(hwnd, WM_CLOSE, 0, 0)
            self.log(f"发送WM_CLOSE消息到窗口 '{window_title}': {result}")

            # 等待一下，检查窗口是否已关闭
            import time
            time.sleep(0.5)

            if not user32.IsWindow(hwnd):
                self.log(f"✓ 窗口 '{window_title}' 已成功关闭")
                return True

            # 方法2: 如果WM_CLOSE失败，尝试强制关闭进程
            self.log(f"窗口 '{window_title}' 未响应关闭消息，尝试查找并关闭进程")

            # 获取窗口的进程ID
            process_id = ctypes.c_ulong()
            user32.GetWindowThreadProcessId(hwnd, ctypes.byref(process_id))
            pid = process_id.value

            if pid > 0:
                self.log(f"窗口 '{window_title}' 对应进程PID: {pid}")
                if self.force_close_process(pid, window_title):
                    return True

            self.log(f"✗ 窗口 '{window_title}' 关闭失败")
            return False

        except Exception as e:
            self.log(f"关闭窗口 '{window_info.get('title', 'Unknown')}' 时出错: {str(e)}")
            return False

    def force_close_process(self, pid, window_title):
        """强制关闭进程"""
        try:
            import subprocess

            # 使用taskkill强制关闭进程
            result = subprocess.run(
                ['taskkill', '/F', '/PID', str(pid)],
                capture_output=True,
                text=True
            )

            if result.returncode == 0:
                self.log(f"✓ 强制关闭进程 {pid} (窗口: '{window_title}') 成功")
                return True
            else:
                self.log(f"✗ 强制关闭进程 {pid} 失败: {result.stderr}")
                return False

        except Exception as e:
            self.log(f"强制关闭进程 {pid} 时出错: {str(e)}")
            return False



    def open_separate_windows(self):
        """并列窗口 - 将已打开的设备窗口平分显示到屏幕"""
        if not WIN_API_AVAILABLE:
            QMessageBox.warning(self, "功能不可用", "Windows API不可用，无法使用并列窗口功能")
            return

        self.log("开始并列窗口排列...")

        try:
            # 获取所有已打开的scrcpy窗口（详细日志模式）
            scrcpy_windows = self.get_all_scrcpy_windows(verbose=True)

            if not scrcpy_windows:
                QMessageBox.information(self, "并列窗口", "没有找到已打开的设备显示窗口")
                return

            # 最多处理8个窗口（2行4列）
            windows_to_arrange = scrcpy_windows[:8]
            self.log(f"找到 {len(scrcpy_windows)} 个设备窗口，将排列前 {len(windows_to_arrange)} 个")

            # 获取主屏幕信息
            screen_info = self.get_primary_screen_info()
            if not screen_info:
                QMessageBox.warning(self, "错误", "无法获取屏幕信息")
                return

            # 计算窗口布局
            layout_info = self.calculate_window_layout(len(windows_to_arrange), screen_info)

            # 排列窗口
            success_count = 0
            for i, window_info in enumerate(windows_to_arrange):
                if self.arrange_window(window_info, i, layout_info):
                    success_count += 1

            self.log(f"并列窗口完成：成功排列 {success_count}/{len(windows_to_arrange)} 个窗口")

            if success_count > 0:
                QMessageBox.information(self, "并列窗口",
                                      f"成功排列 {success_count} 个设备窗口\n"
                                      f"布局：{layout_info['rows']}行 × {layout_info['cols']}列")
            else:
                QMessageBox.warning(self, "并列窗口", "没有成功排列任何窗口")

        except Exception as e:
            self.log(f"并列窗口失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"并列窗口时发生错误：{str(e)}")

    def get_all_scrcpy_windows(self, verbose: bool = False):
        """获取所有scrcpy窗口

        Args:
            verbose: 是否输出详细日志，默认False以减少日志噪音
        """
        if not WIN_API_AVAILABLE:
            return []

        try:
            import ctypes
            user32 = ctypes.windll.user32

            scrcpy_windows = []

            def enum_windows_proc(hwnd, _):
                try:
                    if user32.IsWindowVisible(hwnd):
                        length = user32.GetWindowTextLengthW(hwnd)
                        title = ""
                        if length > 0:
                            buffer = ctypes.create_unicode_buffer(length + 1)
                            user32.GetWindowTextW(hwnd, buffer, length + 1)
                            title = buffer.value

                        class_name = ctypes.create_unicode_buffer(256)
                        user32.GetClassNameW(hwnd, class_name, 256)

                        # 检查是否为scrcpy窗口（SDL_app类名）
                        if class_name.value == "SDL_app":
                            # 获取窗口状态
                            is_minimized = user32.IsIconic(hwnd)

                            scrcpy_windows.append({
                                'hwnd': hwnd,
                                'title': title,
                                'class': class_name.value,
                                'minimized': is_minimized
                            })

                except Exception:
                    pass
                return True

            EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
            user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)

            # 只在详细模式下输出日志，减少日志噪音
            if verbose:
                self.log(f"找到 {len(scrcpy_windows)} 个scrcpy窗口")
                for window in scrcpy_windows:
                    self.log(f"  窗口: {window['title']} (HWND: {window['hwnd']}, 最小化: {window['minimized']})")

            return scrcpy_windows

        except Exception as e:
            self.log(f"获取scrcpy窗口失败: {str(e)}")
            return []

    def get_primary_screen_info(self):
        """获取主屏幕信息"""
        try:
            import ctypes
            from ctypes import wintypes
            user32 = ctypes.windll.user32

            # 获取屏幕尺寸
            screen_width = user32.GetSystemMetrics(0)  # SM_CXSCREEN
            screen_height = user32.GetSystemMetrics(1)  # SM_CYSCREEN

            # 获取工作区域（排除任务栏）
            work_area = wintypes.RECT()
            user32.SystemParametersInfoW(48, 0, ctypes.byref(work_area), 0)  # SPI_GETWORKAREA

            screen_info = {
                'screen_width': screen_width,
                'screen_height': screen_height,
                'work_left': work_area.left,
                'work_top': work_area.top,
                'work_width': work_area.right - work_area.left,
                'work_height': work_area.bottom - work_area.top
            }

            self.log(f"屏幕信息: {screen_width}x{screen_height}, 工作区: {screen_info['work_width']}x{screen_info['work_height']}")
            return screen_info

        except Exception as e:
            self.log(f"获取屏幕信息失败: {str(e)}")
            return None

    def calculate_window_layout(self, window_count, screen_info):
        """计算窗口布局"""
        # 根据窗口数量计算行列数
        if window_count <= 4:
            rows = 1
            cols = window_count
        else:
            rows = 2
            cols = min(4, (window_count + 1) // 2)  # 向上取整

        # 计算每个窗口的尺寸
        window_width = screen_info['work_width'] // cols
        window_height = screen_info['work_height'] // rows

        layout_info = {
            'rows': rows,
            'cols': cols,
            'window_width': window_width,
            'window_height': window_height,
            'work_left': screen_info['work_left'],
            'work_top': screen_info['work_top']
        }

        self.log(f"布局计算: {rows}行 × {cols}列, 每个窗口: {window_width}x{window_height}")
        return layout_info

    def arrange_window(self, window_info, index, layout_info):
        """排列单个窗口"""
        try:
            import ctypes
            user32 = ctypes.windll.user32

            hwnd = window_info['hwnd']

            # 验证窗口句柄是否有效
            if not user32.IsWindow(hwnd):
                self.log(f"窗口句柄 {hwnd} 无效，跳过")
                return False

            # 计算窗口位置
            row = index // layout_info['cols']
            col = index % layout_info['cols']

            x = layout_info['work_left'] + col * layout_info['window_width']
            y = layout_info['work_top'] + row * layout_info['window_height']
            width = layout_info['window_width']
            height = layout_info['window_height']

            self.log(f"排列窗口 '{window_info['title']}' 到位置: ({x}, {y}, {width}, {height})")

            # 如果窗口被最小化，先恢复
            if window_info['minimized']:
                user32.ShowWindow(hwnd, 9)  # SW_RESTORE
                self.log(f"恢复最小化窗口: {window_info['title']}")

            # 设置窗口位置和大小
            # SWP_NOZORDER = 0x0004, SWP_SHOWWINDOW = 0x0040
            SWP_NOZORDER = 0x0004
            SWP_SHOWWINDOW = 0x0040

            result = user32.SetWindowPos(
                hwnd,
                0,  # HWND_TOP
                x, y, width, height,
                SWP_NOZORDER | SWP_SHOWWINDOW
            )

            if result:
                self.log(f"✓ 窗口 '{window_info['title']}' 排列成功")
                return True
            else:
                self.log(f"✗ 窗口 '{window_info['title']}' 排列失败")
                return False

        except Exception as e:
            self.log(f"排列窗口 '{window_info.get('title', 'Unknown')}' 时出错: {str(e)}")
            return False





    def update_all_device_status(self, force_display_check: bool = True):
        """统一的设备状态更新方法

        Args:
            force_display_check: 是否强制检查显示状态，默认True
                                 设为False可以只更新UI而不检查窗口状态
        """
        try:
            # 根据参数决定是否检查显示状态
            scrcpy_windows = []
            if force_display_check:
                scrcpy_windows = self.get_all_scrcpy_windows()

            # 统一遍历设备数据源（self.devices）
            for device in self.devices:
                device_name = device.get('sb_name', '')
                if not device_name:
                    continue

                # 更新显示状态（如果需要检查）
                if force_display_check:
                    is_display_active = any(device_name in window['title'] for window in scrcpy_windows)
                    device['display_active'] = is_display_active

                # 更新对应的设备卡片UI（统一的数据流向）
                if device_name in self.device_cards:
                    self.device_cards[device_name].update_data(device)

        except Exception as e:
            self.log(f"更新设备状态失败: {str(e)}")

    def get_device_by_name(self, device_name: str) -> dict:
        """统一的设备查找方法

        Args:
            device_name: 设备名称

        Returns:
            设备字典，如果未找到返回None
        """
        return next((d for d in self.devices if d.get('sb_name') == device_name), None)

    # 设备操作事件处理
    def on_device_clicked(self, device_name: str):
        """设备点击事件"""
        try:
            modifiers = QApplication.keyboardModifiers()

            if modifiers == Qt.KeyboardModifier.ControlModifier:
                # Ctrl+点击多选
                if device_name in self.selected_devices:
                    self.selected_devices.remove(device_name)
                else:
                    self.selected_devices.add(device_name)
            else:
                # 单选
                self.selected_devices.clear()
                self.selected_devices.add(device_name)

            self.update_selection_display()

            # 自动切换到对应的设备日志标签页
            self.switch_to_device_log_tab(device_name)

        except Exception as e:
            self.log(f"设备点击事件处理失败: {str(e)}")
            import traceback
            self.log(f"错误详情: {traceback.format_exc()}")

    def on_device_double_clicked(self, device_name: str):
        """设备双击事件 - 打开显示或置于前台"""
        try:
            # 实时检测设备显示是否在运行
            self.log(f"设备 {device_name} 双击，实时检测显示状态...")

            if self.has_display_running(device_name):
                # 有显示在运行，尝试置于前台
                self.log(f"设备 {device_name} 检测到显示正在运行，尝试置于前台")
                if self.bring_device_window_to_front(device_name):
                    self.log(f"设备 {device_name} 成功置于前台")
                    return
                else:
                    self.log(f"设备 {device_name} 置于前台失败，尝试重新打开显示")

            # 如果没有显示在运行或置于前台失败，则打开新显示
            self.log(f"设备 {device_name} 没有检测到显示或置于前台失败，打开新显示")
            self.open_device_display(device_name)

        except Exception as e:
            self.log(f"设备 {device_name} 双击处理时发生错误: {str(e)}")
            # 发生错误时，尝试打开新显示作为回退
            try:
                self.open_device_display(device_name)
            except Exception as e2:
                self.log(f"设备 {device_name} 回退操作也失败: {str(e2)}")

    def on_device_right_clicked(self, device_name: str, pos: QPoint):
        """设备右键点击事件"""
        device = self.get_device_by_name(device_name)
        if not device:
            return

        # 确保右键的设备被选中
        if device_name not in self.selected_devices:
            self.selected_devices.clear()
            self.selected_devices.add(device_name)
            self.update_selection_display()

        menu = QMenu(self)
        selected_count = len(self.selected_devices)
        is_multi_select = selected_count > 1

        # 设备信息（仅单选时显示）
        if not is_multi_select:
            info_action = QAction("📋 设备信息", self)
            info_action.triggered.connect(lambda: self.show_device_info(device))
            menu.addAction(info_action)

            # 编辑设备（仅单选时显示）
            edit_action = QAction("✏️ 编辑设备", self)
            edit_action.triggered.connect(lambda: self.edit_device(device))
            menu.addAction(edit_action)

            menu.addSeparator()

        # 多选状态提示
        if is_multi_select:
            selected_list = list(self.selected_devices)[:3]  # 最多显示3个设备名
            display_names = "、".join(selected_list)
            if selected_count > 3:
                display_names += f"等{selected_count}个设备"

            info_action = QAction(f"📋 已选择: {display_names}", self)
            info_action.setEnabled(False)  # 禁用，仅作为信息显示
            menu.addAction(info_action)
            menu.addSeparator()

        # 运行任务
        run_text = f"▶️ 运行任务{'（多选）' if is_multi_select else ''}"
        run_action = QAction(run_text, self)
        if is_multi_select:
            run_action.triggered.connect(lambda: self.run_selected_devices())
        else:
            run_action.triggered.connect(lambda: self.run_device(device_name))
        menu.addAction(run_action)

        # 停止任务
        stop_text = f"⏹️ 停止任务{'（多选）' if is_multi_select else ''}"
        stop_action = QAction(stop_text, self)
        if is_multi_select:
            stop_action.triggered.connect(lambda: self.stop_selected_devices())
        else:
            stop_action.triggered.connect(lambda: self.stop_device(device_name))
        menu.addAction(stop_action)

        menu.addSeparator()

        # 打开显示
        display_text = f"🖥️ 打开显示{'（多选）' if is_multi_select else ''}"
        display_action = QAction(display_text, self)
        if is_multi_select:
            display_action.triggered.connect(lambda: self.open_selected_displays())
        else:
            display_action.triggered.connect(lambda: self.on_device_double_clicked(device_name))
        menu.addAction(display_action)

        # 关闭显示
        close_display_text = f"❌ 关闭显示{'（多选）' if is_multi_select else ''}"
        close_display_action = QAction(close_display_text, self)
        if is_multi_select:
            close_display_action.triggered.connect(lambda: self.close_selected_displays())
        else:
            close_display_action.triggered.connect(lambda: self.close_device_display(device_name))
        menu.addAction(close_display_action)

        menu.addSeparator()

        # 删除设备
        if is_multi_select:
            delete_text = f"🗑️ 删除设备（多选）"
            delete_action = QAction(delete_text, self)
            delete_action.triggered.connect(lambda: self.delete_selected_devices())
        else:
            delete_action = QAction("🗑️ 删除设备", self)
            delete_action.triggered.connect(lambda: self.delete_device(device))
        menu.addAction(delete_action)

        menu.exec(pos)

    def update_selection_display(self):
        """更新选择状态显示 - 使用统一的数据结构"""
        # 统一使用 self.devices 作为数据源
        for device in self.devices:
            device_name = device.get('sb_name', '')
            if device_name and device_name in self.device_cards:
                card = self.device_cards[device_name]
                card.set_selected(device_name in self.selected_devices)

    # 设备操作功能
    def add_device(self):
        """添加设备"""
        dialog = DeviceEditDialog(parent=self)

        # 连接保存信号
        def on_save():
            device_data = dialog.get_data()
            if device_data['sb_name']:
                # 检查设备名称是否重复
                if any(d['sb_name'] == device_data['sb_name'] for d in self.devices):
                    QMessageBox.warning(dialog, "警告", "设备名称已存在，请使用不同的名称")
                    return

                self.devices.append(device_data)
                # 将新设备添加到顺序列表末尾
                device_name = device_data['sb_name']
                if device_name not in self.device_order:
                    self.device_order.append(device_name)
                self.render_devices()
                self.save_config()
                self.log(f"添加设备: {device_name}")
                dialog.close()  # 保存成功后关闭对话框
            else:
                QMessageBox.warning(dialog, "警告", "设备名称不能为空！")

        # 连接按钮信号
        dialog.save_btn.clicked.disconnect()  # 断开原有连接
        dialog.save_btn.clicked.connect(on_save)

        # 显示非模态对话框
        dialog.show()

    def edit_device(self, device: dict):
        """编辑设备"""
        dialog = DeviceEditDialog(device, parent=self)

        # 连接保存信号
        def on_save():
            new_data = dialog.get_data()
            if new_data['sb_name']:
                # 检查设备名称是否重复（排除自己）
                if any(d['sb_name'] == new_data['sb_name'] and d != device for d in self.devices):
                    QMessageBox.warning(dialog, "警告", "设备名称已存在，请使用不同的名称")
                    return

                # 更新设备数据
                old_name = device['sb_name']
                device.update(new_data)

                # 如果名称改变了，需要更新相关记录
                if old_name != new_data['sb_name']:
                    if old_name in self.selected_devices:
                        self.selected_devices.remove(old_name)
                        self.selected_devices.add(new_data['sb_name'])

                self.render_devices()
                self.save_config()
                self.log(f"编辑设备: {new_data['sb_name']}")
                dialog.close()  # 保存成功后关闭对话框
            else:
                QMessageBox.warning(dialog, "警告", "设备名称不能为空！")

        # 连接按钮信号
        dialog.save_btn.clicked.disconnect()  # 断开原有连接
        dialog.save_btn.clicked.connect(on_save)

        # 显示非模态对话框
        dialog.show()

    def delete_device(self, device: dict):
        """删除设备"""
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除设备 \"{device['sb_name']}\" 吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            device_name = device['sb_name']

            # 停止相关进程
            self.stop_device(device_name)
            self.close_device_display(device_name)

            # 从列表中移除
            self.devices.remove(device)
            self.selected_devices.discard(device_name)

            # 从设备顺序中移除
            if device_name in self.device_order:
                self.device_order.remove(device_name)

            self.render_devices()
            self.save_config()
            self.log(f"删除设备: {device_name}")

    def show_device_info(self, device: dict):
        """显示设备信息"""
        device_name = device.get('sb_name', '未知')

        # 基本设备信息
        info_text = f"""设备信息：

名称: {device_name}
URL: {', '.join(device.get('sb_urls', []))}
当前URL索引: {device.get('current_url_index', 0)}
ADB连接: {'已连接' if device.get('adb_connected', False) else '未连接'}
显示状态: {'已显示' if device.get('display_active', False) else '未显示'}
任务状态: {'运行中' if device.get('task_running', False) else '未运行'}

备注: {device.get('notes', '无')}"""

        # 添加手机卡信息
        phone_cards = self.get_device_phone_cards(device_name)
        if phone_cards:
            info_text += "\n\n📱 关联手机卡："
            for card_id, card_data in phone_cards.items():
                phone_number = card_data.get('phone_number', '未知')
                tags = card_data.get('tags', {})
                tag_text = ', '.join([f"{k}:{v}" for k, v in tags.items()]) if tags else '无标签'
                info_text += f"\n  • {phone_number} ({tag_text})"
        else:
            info_text += "\n\n📱 关联手机卡: 无"

        # 添加APP账号信息
        app_accounts = self.get_device_app_accounts(device_name)
        if app_accounts:
            info_text += "\n\n📱 关联APP账号："
            for account_id, account_data in app_accounts.items():
                app_name = account_data.get('app_name', '未知')
                phone_number = account_data.get('phone_number', '')
                phone_text = f" [手机号:{phone_number}]" if phone_number else " [无手机号]"
                app_type = account_data.get('app_type', 'original')
                type_text = "原应用" if app_type == "original" else "分身应用"
                tags = account_data.get('tags', {})
                tag_text = ', '.join([f"{k}:{v}" for k, v in tags.items()]) if tags else '无标签'
                info_text += f"\n  • {app_name}{phone_text} ({type_text}) - {tag_text}"
        else:
            info_text += "\n\n📱 关联APP账号: 无"

        # 使用自定义对话框显示信息，支持文本选择和复制
        self.show_device_info_dialog(device_name, info_text)

    def show_device_info_dialog(self, device_name, info_text):
        """显示设备信息对话框"""
        dialog = QDialog(self)
        dialog.setWindowTitle(f"设备信息 - {device_name}")
        dialog.setMinimumSize(500, 400)
        dialog.resize(500, 400)

        layout = QVBoxLayout(dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 信息文本区域
        info_text_edit = QTextEdit()
        info_text_edit.setReadOnly(True)
        info_text_edit.setPlainText(info_text)
        info_text_edit.setStyleSheet("""
            QTextEdit {
                border: 1px solid #dee2e6;
                border-radius: 4px;
                background: #f8f9fa;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                color: #333;
                padding: 10px;
            }
            QTextEdit::selection {
                background-color: #007bff;
                color: white;
            }
        """)
        layout.addWidget(info_text_edit)

        # 按钮布局
        button_layout = QHBoxLayout()

        # 复制按钮
        copy_btn = QPushButton("📋 复制信息")
        copy_btn.clicked.connect(lambda: self.copy_to_clipboard(info_text))
        button_layout.addWidget(copy_btn)

        button_layout.addStretch()

        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(dialog.close)
        close_btn.setDefault(True)
        button_layout.addWidget(close_btn)

        layout.addLayout(button_layout)

        # 设置样式
        dialog.setStyleSheet("""
            QDialog {
                background: white;
            }
            QPushButton {
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                font-size: 13px;
                font-weight: 500;
                min-width: 80px;
            }
            QPushButton#copy_btn {
                background: #28a745;
                color: white;
            }
            QPushButton#copy_btn:hover {
                background: #218838;
            }
            QPushButton#close_btn {
                background: #6c757d;
                color: white;
            }
            QPushButton#close_btn:hover {
                background: #5a6268;
            }
        """)

        copy_btn.setObjectName("copy_btn")
        close_btn.setObjectName("close_btn")

        dialog.exec()

    def copy_to_clipboard(self, text):
        """复制文本到剪贴板"""
        try:
            from PyQt6.QtGui import QClipboard
            clipboard = QApplication.clipboard()
            clipboard.setText(text)
            self.log("设备信息已复制到剪贴板")
        except Exception as e:
            self.log(f"复制失败: {str(e)}")

    # ADB和进程管理功能
    def _init_reconnect_tracking(self):
        """初始化重连状态跟踪"""
        self._reconnect_attempts = {}
        self._last_reconnect_time = {}
        self._offline_reconnect_attempts = {}
        self._last_offline_reconnect_time = {}
        self.log("🔄 重连状态跟踪已初始化")

    def _reset_device_reconnect_status(self, device_name: str):
        """重置指定设备的重连状态"""
        if hasattr(self, '_reconnect_attempts'):
            self._reconnect_attempts[device_name] = 0
        if hasattr(self, '_last_reconnect_time'):
            self._last_reconnect_time[device_name] = 0
        if hasattr(self, '_offline_reconnect_attempts'):
            self._offline_reconnect_attempts[device_name] = 0
        if hasattr(self, '_last_offline_reconnect_time'):
            self._last_offline_reconnect_time[device_name] = 0

    def force_refresh_adb_status(self):
        """强制刷新ADB状态 - 用于手动刷新按钮"""
        # 重置刷新标志，强制执行刷新
        if hasattr(self, '_refreshing_adb'):
            self._refreshing_adb = False

        self.log("🔄 手动强制刷新ADB状态...")
        self.refresh_adb_status()

    def refresh_adb_status(self):
        """刷新ADB连接状态 - 并发版本"""
        # 防止重复刷新
        if hasattr(self, '_refreshing_adb') and self._refreshing_adb:
            self.log("ADB刷新正在进行中，跳过重复请求")
            return

        def refresh_worker():
            try:
                self._refreshing_adb = True
                self.log("开始刷新ADB连接状态...")

                # 获取当前已连接的设备列表
                result = subprocess.run(
                    "adb devices",
                    shell=True,
                    capture_output=True,
                    text=True,
                    timeout=10,
                    encoding='utf-8',
                    errors='replace'
                )

                if result.returncode != 0:
                    self.log(f"ADB命令执行失败，返回码: {result.returncode}")
                    return

                # 解析ADB输出
                current_connected_devices = set()
                offline_devices = set()
                lines = result.stdout.strip().split('\n')

                for i, line in enumerate(lines):
                    if i == 0 or not line.strip():
                        continue

                    if '\t' in line or '    ' in line:
                        if '\t' in line:
                            parts = line.split('\t')
                        else:
                            parts = line.split()

                        if len(parts) >= 2:
                            device_id = parts[0].strip()
                            device_status = parts[1].strip()

                            if device_status == 'device':
                                current_connected_devices.add(device_id)
                            elif device_status == 'offline':
                                offline_devices.add(device_id)
                                self.log(f"⚠️ 检测到离线设备: {device_id}")

                # 记录检测到的设备状态
                if current_connected_devices:
                    self.log(f"✓ 检测到 {len(current_connected_devices)} 个在线设备: {', '.join(current_connected_devices)}")
                if offline_devices:
                    self.log(f"⚠️ 检测到 {len(offline_devices)} 个离线设备: {', '.join(offline_devices)}")

                # 在处理设备连接前，先统一处理TCPIP端口
                self._standardize_tcpip_ports(current_connected_devices)

                # 重新获取设备列表（因为端口可能已改变）
                updated_result = subprocess.run(
                    "adb devices",
                    shell=True,
                    capture_output=True,
                    text=True,
                    timeout=10,
                    encoding='utf-8',
                    errors='replace'
                )

                if updated_result.returncode == 0:
                    # 重新解析更新后的设备列表
                    current_connected_devices = set()
                    offline_devices = set()
                    updated_lines = updated_result.stdout.strip().split('\n')

                    for i, line in enumerate(updated_lines):
                        if i == 0 or not line.strip():
                            continue

                        if '\t' in line or '    ' in line:
                            if '\t' in line:
                                parts = line.split('\t')
                            else:
                                parts = line.split()

                            if len(parts) >= 2:
                                device_id = parts[0].strip()
                                device_status = parts[1].strip()

                                if device_status == 'device':
                                    current_connected_devices.add(device_id)
                                elif device_status == 'offline':
                                    offline_devices.add(device_id)

                    # 更新日志信息
                    if current_connected_devices:
                        self.log(f"✓ 端口标准化后检测到 {len(current_connected_devices)} 个在线设备")
                    if offline_devices:
                        self.log(f"⚠️ 端口标准化后检测到 {len(offline_devices)} 个离线设备")

                # 并发处理设备连接检查和重连
                self._process_devices_concurrent(current_connected_devices, offline_devices)

            except Exception as e:
                self.log(f"ADB状态检查失败: {str(e)}")
            finally:
                self._refreshing_adb = False
                # 在主线程中更新UI
                QTimer.singleShot(0, self._update_ui_after_adb_refresh)

        # 在后台线程中执行
        threading.Thread(target=refresh_worker, daemon=True).start()

    def _standardize_tcpip_ports(self, connected_devices: set):
        """统一处理设备TCPIP端口，将所有设备端口标准化为5678"""
        target_port = 5678
        devices_to_process = []

        QTimer.singleShot(0, lambda: self.log("开始统一设备TCPIP端口..."))

        # 分析需要处理的设备
        for device_id in connected_devices:
            device_info = self._analyze_device_for_port_standardization(device_id, target_port)
            if device_info:
                devices_to_process.append(device_info)

        if not devices_to_process:
            QTimer.singleShot(0, lambda: self.log("所有设备端口已标准化，无需处理"))
            return

        QTimer.singleShot(0, lambda: self.log(f"发现 {len(devices_to_process)} 个设备需要端口标准化"))

        # 批量处理设备端口
        success_count = 0
        for device_info in devices_to_process:
            device_id = device_info['device_id']
            device_type = device_info['device_type']

            QTimer.singleShot(0, lambda did=device_id, dtype=device_type:
                self.log(f"正在处理{dtype}设备 {did} 的端口..."))

            try:
                # 设置TCPIP端口
                tcpip_result = subprocess.run(
                    f"adb -s {device_id} tcpip {target_port}",
                    shell=True,
                    capture_output=True,
                    text=True,
                    timeout=10,
                    encoding='utf-8',
                    errors='replace'
                )

                if tcpip_result.returncode == 0:
                    QTimer.singleShot(0, lambda did=device_id:
                        self.log(f"✓ 设备 {did} 端口已标准化为 {target_port}"))
                    success_count += 1
                else:
                    error_msg = tcpip_result.stderr.strip() if tcpip_result.stderr else "未知错误"
                    QTimer.singleShot(0, lambda did=device_id, err=error_msg:
                        self.log(f"⚠️ 设备 {did} 端口标准化失败: {err}"))

            except subprocess.TimeoutExpired:
                QTimer.singleShot(0, lambda did=device_id:
                    self.log(f"⚠️ 设备 {did} 端口标准化超时"))
            except Exception as e:
                QTimer.singleShot(0, lambda did=device_id, err=str(e):
                    self.log(f"⚠️ 设备 {did} 端口标准化出错: {err}"))

        QTimer.singleShot(0, lambda: self.log(f"端口标准化完成: {success_count}/{len(devices_to_process)} 个设备成功"))

        # 等待端口切换生效
        if success_count > 0:
            QTimer.singleShot(0, lambda: self.log("等待端口切换生效..."))
            time.sleep(2)

    def _check_tcpip_port_status(self, device_id: str, target_port: int) -> bool:
        """检查设备是否已经开启了指定的tcpip端口"""
        try:
            # 使用adb shell netstat命令检查端口状态
            result = subprocess.run(
                f'adb -s {device_id} shell netstat -tuln | findstr ":{target_port}"',
                shell=True,
                capture_output=True,
                text=True,
                timeout=5,
                encoding='utf-8',
                errors='replace'
            )

            if result.returncode == 0 and result.stdout.strip():
                # 检查输出中是否包含LISTEN状态，表示端口已开启
                output_lines = result.stdout.strip().split('\n')
                for line in output_lines:
                    if 'LISTEN' in line and f':{target_port}' in line:
                        QTimer.singleShot(0, lambda did=device_id, port=target_port:
                            self.log(f"✓ 设备 {did} 的tcpip端口 {port} 已开启"))
                        return True

            return False

        except Exception as e:
            QTimer.singleShot(0, lambda did=device_id, err=str(e):
                self.log(f"⚠️ 检查设备 {did} tcpip端口状态失败: {err}"))
            return False

    def _analyze_device_for_port_standardization(self, device_id: str, target_port: int) -> dict:
        """分析设备是否需要端口标准化"""
        # 跳过域名设备
        if self._is_domain_device(device_id):
            QTimer.singleShot(0, lambda did=device_id:
                self.log(f"跳过域名设备: {did}"))
            return None

        # USB设备需要处理
        if ':' not in device_id:
            # 先检查USB设备是否已经开启了tcpip端口
            if self._check_tcpip_port_status(device_id, target_port):
                QTimer.singleShot(0, lambda did=device_id, port=target_port:
                    self.log(f"设备 {did} tcpip端口 {port} 已开启，跳过"))
                return None

            return {
                'device_id': device_id,
                'device_type': 'USB',
                'current_port': None
            }

        # IP设备检查端口
        if self._is_ip_device(device_id):
            current_port = device_id.split(':')[-1]

            # 如果当前端口已经是目标端口，则不需要处理
            if current_port == str(target_port):
                QTimer.singleShot(0, lambda did=device_id:
                    self.log(f"设备 {did} 端口已是 {target_port}，跳过"))
                return None

            return {
                'device_id': device_id,
                'device_type': 'IP',
                'current_port': current_port
            }

        return None

    def _is_domain_device(self, device_id: str) -> bool:
        """判断是否是域名设备"""
        # 域名设备：包含字母且有点和冒号
        if any(c.isalpha() for c in device_id.replace('.', '').replace(':', '')):
            if '.' in device_id and ':' in device_id:
                return True
        return False

    def _is_ip_device(self, device_id: str) -> bool:
        """判断是否是IP设备"""
        if ':' not in device_id:
            return False

        # 简单的IP格式检查
        ip_part = device_id.split(':')[0]
        parts = ip_part.split('.')

        if len(parts) != 4:
            return False

        try:
            for part in parts:
                num = int(part)
                if num < 0 or num > 255:
                    return False
            return True
        except ValueError:
            return False

    def _process_devices_concurrent(self, current_connected_devices: set, offline_devices: set = None):
        """并发处理设备连接状态 - 支持多URL智能切换"""
        if offline_devices is None:
            offline_devices = set()

        connected_count = 0
        devices_to_reconnect = []

        # 第一阶段：智能检查所有设备的连接状态
        for device in self.devices:
            try:
                device_name = device['sb_name']
                urls = device.get('sb_urls', [])
                current_url_index = device.get('current_url_index', 0)

                if not urls:
                    device['adb_connected'] = False
                    self.device_adb_status[device_name] = False
                    continue

                # 检查设备是否在离线列表中
                device_offline = any(url in offline_devices for url in urls)
                if device_offline:
                    device['adb_connected'] = False
                    self.device_adb_status[device_name] = False
                    offline_url = next((url for url in urls if url in offline_devices), "未知")

                    # 检查是否应该重连离线设备
                    if not hasattr(self, '_offline_reconnect_attempts'):
                        self._offline_reconnect_attempts = {}
                    if not hasattr(self, '_last_offline_reconnect_time'):
                        self._last_offline_reconnect_time = {}

                    attempts = self._offline_reconnect_attempts.get(device_name, 0)
                    last_time = self._last_offline_reconnect_time.get(device_name, 0)
                    current_time = time.time()

                    # 离线设备重连策略：每60秒重试一次
                    should_reconnect = attempts == 0 or (current_time - last_time > 60)

                    if should_reconnect:
                        self.log(f"⚠️ 设备 {device_name} 处于离线状态 ({offline_url})，尝试重连...")
                        self._last_offline_reconnect_time[device_name] = current_time
                        self._offline_reconnect_attempts[device_name] = attempts + 1

                        # 尝试重连离线设备
                        self._try_reconnect_offline_device(device_name, offline_url)
                    else:
                        time_since_last = int(current_time - last_time)
                        self.log(f"⚠️ 设备 {device_name} 离线，等待重连 (距离上次 {time_since_last}秒)")

                    continue

                # 检查所有URL是否有已连接的
                connected_url_index = None
                for i, url in enumerate(urls):
                    if url in current_connected_devices:
                        connected_url_index = i
                        break

                if connected_url_index is not None:
                    # 找到已连接的URL，更新current_url_index
                    if connected_url_index != current_url_index:
                        device['current_url_index'] = connected_url_index
                        self.log(f"设备 {device_name} 自动切换到URL[{connected_url_index}]: {urls[connected_url_index]}")

                    device['adb_connected'] = True
                    self.device_adb_status[device_name] = True
                    connected_count += 1

                    # 获取电池电量（异步，不阻塞）
                    current_url = urls[connected_url_index]
                    threading.Thread(
                        target=self._get_battery_level_async,
                        args=(device_name, current_url),
                        daemon=True
                    ).start()
                else:
                    # 没有找到已连接的URL，标记为未连接并准备重连
                    device['adb_connected'] = False
                    self.device_adb_status[device_name] = False

                    # 收集需要重连的设备（包含所有URL信息）
                    if not hasattr(self, '_reconnect_attempts'):
                        self._reconnect_attempts = {}
                    if not hasattr(self, '_last_reconnect_time'):
                        self._last_reconnect_time = {}

                    # 获取重连尝试次数和最后重连时间
                    attempts = self._reconnect_attempts.get(device_name, 0)
                    last_time = self._last_reconnect_time.get(device_name, 0)
                    current_time = time.time()

                    # 改进的重连策略：
                    # 1. 前3次立即重连
                    # 2. 之后每30秒重连一次，持续尝试
                    should_reconnect = False
                    if attempts < 3:
                        should_reconnect = True
                        self.log(f"设备 {device_name} 准备立即重连 (尝试 {attempts + 1}/3)")
                    elif current_time - last_time > 30:  # 30秒后重试
                        should_reconnect = True
                        self.log(f"设备 {device_name} 准备定时重连 (距离上次 {int(current_time - last_time)}秒)")

                    if should_reconnect:
                        devices_to_reconnect.append((device_name, device))
                        self._last_reconnect_time[device_name] = current_time

            except Exception as e:
                self.log(f"检查设备 {device.get('sb_name', 'Unknown')} 状态时出错: {str(e)}")

        # 第二阶段：并发重连未连接的设备
        if devices_to_reconnect:
            self.log(f"开始并发重连 {len(devices_to_reconnect)} 个设备...")

            with concurrent.futures.ThreadPoolExecutor(max_workers=min(5, len(devices_to_reconnect))) as executor:
                # 提交所有重连任务
                future_to_device = {
                    executor.submit(self.try_connect_device_multi_url, device): device_name
                    for device_name, device in devices_to_reconnect
                }

                # 收集结果
                for future in concurrent.futures.as_completed(future_to_device, timeout=60):
                    device_name = future_to_device[future]
                    try:
                        success, connected_url_index = future.result()
                        if success:
                            # 更新设备状态
                            device = next((d for d in self.devices if d['sb_name'] == device_name), None)
                            if device:
                                device['adb_connected'] = True
                                device['current_url_index'] = connected_url_index
                                self.device_adb_status[device_name] = True
                                connected_count += 1

                                # 重置重连状态
                                self._reset_device_reconnect_status(device_name)

                                # 获取电池电量（异步，不阻塞）
                                urls = device.get('sb_urls', [])
                                if connected_url_index < len(urls):
                                    current_url = urls[connected_url_index]
                                    threading.Thread(
                                        target=self._get_battery_level_async,
                                        args=(device_name, current_url),
                                        daemon=True
                                    ).start()
                        else:
                            # 重连失败，增加计数但不限制重试次数
                            if hasattr(self, '_reconnect_attempts'):
                                self._reconnect_attempts[device_name] = self._reconnect_attempts.get(device_name, 0) + 1
                    except Exception as e:
                        self.log(f"重连设备 {device_name} 时出错: {str(e)}")
                        self._reconnect_attempts[device_name] = self._reconnect_attempts.get(device_name, 0) + 1

        self.log(f"ADB状态刷新完成: {connected_count}/{len(self.devices)} 个设备已连接")

    def _get_battery_level_async(self, device_name: str, device_url: str):
        """异步获取设备电池电量和充电状态"""
        try:
            # 使用adb命令获取完整电池信息
            result = subprocess.run(
                f"adb -s {device_url} shell dumpsys battery",
                shell=True,
                capture_output=True,
                text=True,
                timeout=5,
                encoding='utf-8',
                errors='replace'
            )

            if result.returncode == 0 and result.stdout.strip():
                # 解析电池信息
                output = result.stdout.strip()
                battery_level = None
                is_charging = False

                for line in output.split('\n'):
                    line = line.strip()

                    # 解析电池电量
                    if line.startswith('level:'):
                        try:
                            battery_level = int(line.split(':')[1].strip())
                        except (ValueError, IndexError):
                            continue

                    # 解析充电状态
                    elif line.startswith('AC powered:') and 'true' in line:
                        is_charging = True
                    elif line.startswith('USB powered:') and 'true' in line:
                        is_charging = True
                    elif line.startswith('Wireless powered:') and 'true' in line:
                        is_charging = True
                    elif line.startswith('status:'):
                        try:
                            status = int(line.split(':')[1].strip())
                            # status: 2=充电中, 3=未充电, 4=未充电, 5=充满
                            if status == 2:  # 充电中
                                is_charging = True
                        except (ValueError, IndexError):
                            continue

                # 如果成功解析到电池电量，发送信号更新UI
                if battery_level is not None:
                    self.log_signals.battery_level_updated.emit(device_name, battery_level, is_charging)
                    return

        except Exception:
            # 静默处理错误，不影响主程序
            pass

    def _update_battery_level(self, device_name: str, battery_level: int, is_charging: bool = False):
        """在主线程中更新设备电池电量和充电状态"""
        try:
            # 更新设备数据
            for device in self.devices:
                if device.get('sb_name') == device_name:
                    device['battery_level'] = battery_level
                    device['is_charging'] = is_charging
                    break

            # 更新设备卡片显示
            if device_name in self.device_cards:
                self.device_cards[device_name].update_battery_display()

        except Exception:
            # 静默处理错误
            pass

    def _try_reconnect_offline_device(self, device_name: str, offline_url: str):
        """尝试重连离线设备"""
        try:
            # 先断开连接
            disconnect_result = subprocess.run(
                f"adb disconnect {offline_url}",
                shell=True,
                capture_output=True,
                text=True,
                timeout=5,
                encoding='utf-8',
                errors='replace'
            )

            # 等待一下
            time.sleep(1)

            # 重新连接
            connect_result = subprocess.run(
                f"adb connect {offline_url}",
                shell=True,
                capture_output=True,
                text=True,
                timeout=10,
                encoding='utf-8',
                errors='replace'
            )

            if connect_result.returncode == 0 and "connected" in connect_result.stdout.lower():
                self.log(f"✓ 设备 {device_name} ({offline_url}) 重连成功")
                # 更新设备状态
                device = self.get_device_by_name(device_name)
                if device:
                    device['adb_connected'] = True
                    self.device_adb_status[device_name] = True

                    # 重置重连状态
                    self._reset_device_reconnect_status(device_name)
            else:
                error_msg = connect_result.stderr.strip() if connect_result.stderr else connect_result.stdout.strip()
                self.log(f"✗ 设备 {device_name} ({offline_url}) 重连失败: {error_msg}")

        except Exception as e:
            self.log(f"✗ 设备 {device_name} ({offline_url}) 重连出错: {str(e)}")

    def _update_ui_after_adb_refresh(self):
        """ADB刷新完成后更新UI"""
        try:
            # 强制更新所有设备卡片的显示状态
            self.update_all_device_status(force_display_check=False)

            # 强制刷新设备卡片UI
            for device_name, card in self.device_cards.items():
                device = self.get_device_by_name(device_name)
                if device:
                    card.update_data(device)

            self.log("UI状态更新完成")
        except Exception as e:
            self.log(f"UI更新失败: {str(e)}")

    def try_connect_device_multi_url(self, device: dict) -> tuple[bool, int]:
        """尝试连接设备的多个URL - 智能切换版本"""
        device_name = device['sb_name']
        urls = device.get('sb_urls', [])
        current_url_index = device.get('current_url_index', 0)

        if not urls:
            QTimer.singleShot(0, lambda name=device_name: self.log(f"⚠️ 设备 {name} 没有配置URL"))
            return False, -1

        # 优先尝试当前URL，然后尝试其他URL
        url_order = [current_url_index] + [i for i in range(len(urls)) if i != current_url_index]

        for url_index in url_order:
            if url_index >= len(urls):
                continue

            device_url = urls[url_index]
            QTimer.singleShot(0, lambda name=device_name, url=device_url, idx=url_index:
                self.log(f"正在尝试连接设备 {name} URL[{idx}]: {url}..."))

            if self.try_connect_single_url(device_url, device_name):
                QTimer.singleShot(0, lambda name=device_name, url=device_url, idx=url_index:
                    self.log(f"✓ 设备 {name} 通过URL[{idx}]连接成功: {url}"))
                return True, url_index

        QTimer.singleShot(0, lambda name=device_name: self.log(f"✗ 设备 {name} 所有URL连接失败"))
        return False, -1

    def try_connect_single_url(self, device_url: str, device_name: str) -> bool:
        """尝试连接单个URL"""
        try:
            # 减少超时时间，避免长时间阻塞
            result = subprocess.run(
                f"adb connect {device_url}",
                shell=True,
                capture_output=True,
                text=True,
                timeout=6,
                encoding='utf-8',
                errors='replace'
            )

            if result.returncode == 0:
                output = result.stdout.strip()
                if "connected" in output.lower() or "already connected" in output.lower():
                    # 减少等待时间
                    time.sleep(0.3)

                    # 验证连接，使用更短的超时
                    verify_result = subprocess.run(
                        "adb devices",
                        shell=True,
                        capture_output=True,
                        text=True,
                        timeout=2,
                        encoding='utf-8',
                        errors='replace'
                    )

                    if verify_result.returncode == 0:
                        if device_url in verify_result.stdout and "device" in verify_result.stdout:
                            return True

        except subprocess.TimeoutExpired:
            pass
        except Exception:
            pass

        return False

    def try_connect_device(self, device_url: str, device_name: str) -> bool:
        """尝试连接指定的设备URL - 兼容旧接口"""
        return self.try_connect_single_url(device_url, device_name)

    def run_device(self, device_name: str):
        """运行指定设备"""
        device = self.get_device_by_name(device_name)
        if not device:
            return

        if device_name in self.running_processes:
            process = self.running_processes[device_name]
            if process.poll() is None:
                self.log(f"设备 {device_name} 已在运行中")
                return
            else:
                del self.running_processes[device_name]

        # 显示运行任务配置对话框
        dialog = RunTaskDialog(device, parent=self)
        if dialog.exec() != QDialog.DialogCode.Accepted:
            return

        # 获取用户选择的运行配置
        run_config = dialog.get_run_config()

        # 保存用户的选择作为下次的默认配置
        self.last_run_config = run_config.copy()

        # 确保设备有日志标签页
        self.ensure_device_log_tab(device_name)

        # 临时更新设备配置用于生成命令
        original_config = {
            'need_open_app': device.get('need_open_app', False),
            'is_clone': device.get('is_clone', False),
            'action': device.get('action', 'do_job')
        }

        # 应用用户选择的配置
        device.update(run_config)

        try:
            command = self.generate_command(device)
            self.log(f"启动设备 {device_name}: {command}")

            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONUNBUFFERED'] = '1'

            process = subprocess.Popen(
                command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                encoding='utf-8',
                errors='replace',
                cwd=os.getcwd(),
                env=env,
                bufsize=0
            )

            self.running_processes[device_name] = process
            device['task_running'] = True

            self.log(f"设备 {device_name} 启动成功 (PID: {process.pid})")
            self.log(f"运行配置: 开启应用={run_config['need_open_app']}, 分身模式={run_config['is_clone']}, 动作={run_config['action']}")

            # 检查是否需要启动防休眠
            self._check_running_devices()

            # 启动监控线程
            threading.Thread(target=self.monitor_device_output, args=(device_name, process), daemon=True).start()

        except Exception as e:
            self.log(f"启动设备 {device_name} 失败: {str(e)}")
        finally:
            # 恢复原始配置（不保存临时的运行配置到设备配置中）
            device.update(original_config)

    def generate_command(self, device: dict) -> str:
        """生成命令行指令"""
        device_name = device.get('sb_name', '未知设备')
        current_url_index = device.get('current_url_index', 0)
        urls = device.get('sb_urls', [])

        if current_url_index < len(urls):
            current_url = urls[current_url_index]
        else:
            current_url = urls[0] if urls else "无URL"

        cmd_parts = ['python', 'do_job.py']
        cmd_parts.extend(['--sb_name', f'"{device_name}"'])
        cmd_parts.extend(['--sb_url', f'"{current_url}"'])

        if device.get('need_open_app', False):
            cmd_parts.append('--need_open_app')

        if device.get('is_clone', False):
            cmd_parts.append('--is_clone')

        if device.get('action'):
            cmd_parts.extend(['--action', f'"{device["action"]}"'])

        return ' '.join(cmd_parts)

    def monitor_device_output(self, device_name: str, process: subprocess.Popen):
        """监控设备进程输出"""
        try:
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    output = output.strip()
                    if output:
                        # 使用线程安全的日志记录
                        self.log_from_thread(f"[{device_name}] {output}", device_name)

            # 进程结束
            return_code = process.poll()
            self.log_from_thread(f"设备 {device_name} 进程结束 (返回码: {return_code})", device_name)

            # 更新状态
            device = next((d for d in self.devices if d['sb_name'] == device_name), None)
            if device:
                device['task_running'] = False

            # 清理进程记录
            if device_name in self.running_processes:
                del self.running_processes[device_name]

        except Exception as e:
            self.log_from_thread(f"监控设备 {device_name} 输出时出错: {str(e)}", device_name)

    def stop_device(self, device_name: str):
        """停止指定设备"""
        if device_name not in self.running_processes:
            return

        try:
            process = self.running_processes[device_name]

            if process.poll() is None:
                if os.name == 'nt':
                    subprocess.run(f'taskkill /F /T /PID {process.pid}', shell=True)
                else:
                    process.terminate()
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        process.kill()

                self.log(f"设备 {device_name} 已停止")

            # 更新状态
            device = next((d for d in self.devices if d['sb_name'] == device_name), None)
            if device:
                device['task_running'] = False

            del self.running_processes[device_name]

            # 检查是否需要停止防休眠
            self._check_running_devices()

        except Exception as e:
            self.log(f"停止设备 {device_name} 失败: {str(e)}")

    # scrcpy显示管理
    def open_device_display(self, device_name: str):
        """打开指定设备的显示"""
        device = self.get_device_by_name(device_name)
        if not device:
            return

        # 检查是否已有显示运行
        if self.has_display_running(device_name):
            self.log(f"设备 {device_name} 的显示已在运行")
            return

        try:
            command = self.generate_scrcpy_command(device)
            self.log(f"启动设备显示 {device_name}: {command}")

            process = subprocess.Popen(
                command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                errors='replace'
            )

            device['display_active'] = True
            self.log(f"设备 {device_name} 显示已启动 (PID: {process.pid})")

            # 启动监控线程
            threading.Thread(target=self.monitor_scrcpy_process, args=(device_name, process), daemon=True).start()

        except Exception as e:
            self.log(f"启动设备显示 {device_name} 失败: {str(e)}")

    def generate_scrcpy_command(self, device: dict) -> str:
        """生成scrcpy命令"""
        device_name = device['sb_name']
        current_url_index = device.get('current_url_index', 0)
        urls = device.get('sb_urls', [])

        if current_url_index < len(urls):
            device_url = urls[current_url_index]
        else:
            device_url = urls[0] if urls else "无URL"

        # 判断是否为10x设备
        is_10x_device = '10x' in device_name.lower()

        scrcpy_path = r"D:\Android-adb\scrcpy-win64-v3.1\scrcpy.exe"

        if is_10x_device:
            command = f'"{scrcpy_path}" -s {device_url} -w -b 2M --shortcut-mod=lctrl --no-audio --window-title "{device_name}"'
        else:
            command = f'"{scrcpy_path}" -s {device_url} -Sw -b 2M --shortcut-mod=lctrl --no-audio --window-title "{device_name}"'

        return command

    def monitor_scrcpy_process(self, device_name: str, process: subprocess.Popen):
        """监控scrcpy进程状态"""
        try:
            process.wait()

            # 更新设备状态
            device = next((d for d in self.devices if d['sb_name'] == device_name), None)
            if device:
                device['display_active'] = False

            self.log(f"设备 {device_name} 的显示进程已结束")

        except Exception as e:
            self.log(f"监控设备 {device_name} 显示进程时出错: {str(e)}")

    def close_device_display(self, device_name: str):
        """关闭指定设备的显示 - 使用实时检测"""
        if not WIN_API_AVAILABLE:
            self.log(f"无法关闭设备 {device_name} 的显示：Windows API不可用")
            return

        try:
            # 查找该设备的scrcpy窗口
            scrcpy_windows = self.get_all_scrcpy_windows()
            device_windows = [w for w in scrcpy_windows if device_name in w['title']]

            if not device_windows:
                self.log(f"未找到设备 {device_name} 的显示窗口")
                return

            # 关闭找到的窗口
            success_count = 0
            for window_info in device_windows:
                if self.close_window(window_info):
                    success_count += 1
                    self.log(f"设备 {device_name} 的显示窗口已关闭")
                else:
                    self.log(f"关闭设备 {device_name} 的窗口失败")

            if success_count > 0:
                # 更新设备状态
                device = next((d for d in self.devices if d['sb_name'] == device_name), None)
                if device:
                    device['display_active'] = False

        except Exception as e:
            self.log(f"关闭设备 {device_name} 显示失败: {str(e)}")



    def bring_device_window_to_front(self, device_name: str):
        """将指定设备的窗口置于前台 - 统一使用实时检测"""
        if not WIN_API_AVAILABLE:
            self.log("Windows API不可用，无法将窗口置于前台")
            return False

        try:
            self.log(f"设备 {device_name} 开始实时查找scrcpy窗口...")

            # 统一使用实时检测，不依赖存储的进程信息
            scrcpy_windows = self.get_all_scrcpy_windows()
            device_windows = [w for w in scrcpy_windows if device_name in w['title']]
            hwnd = device_windows[0]['hwnd'] if device_windows else None

            if hwnd:
                self.log(f"找到设备 {device_name} 的窗口: {hwnd}")

                # 尝试将窗口置于前台
                if self.bring_window_to_front(hwnd):
                    self.log(f"设备 {device_name} 的显示窗口已置于前台")
                    return True
                else:
                    self.log(f"设备 {device_name} 找到窗口但置于前台失败")
                    return False
            else:
                self.log(f"没有找到设备 {device_name} 的scrcpy窗口")
                return False

        except Exception as e:
            self.log(f"将设备 {device_name} 窗口置于前台时出错: {str(e)}")
            return False


    def bring_window_to_front(self, hwnd):
        """将窗口置于前台"""
        if not WIN_API_AVAILABLE or not hwnd:
            self.log(f"无法置于前台: WIN_API_AVAILABLE={WIN_API_AVAILABLE}, hwnd={hwnd}")
            return False

        try:
            import ctypes
            user32 = ctypes.windll.user32
            kernel32 = ctypes.windll.kernel32

            self.log(f"开始将窗口 {hwnd} 置于前台...")

            # 验证窗口句柄是否有效
            try:
                if not user32.IsWindow(hwnd):
                    self.log(f"窗口句柄 {hwnd} 无效")
                    return False
            except Exception as e:
                self.log(f"验证窗口句柄时出错: {e}")
                return False

            # 获取当前窗口状态
            is_minimized = user32.IsIconic(hwnd)
            is_maximized = user32.IsZoomed(hwnd)
            is_visible = user32.IsWindowVisible(hwnd)

            self.log(f"窗口状态 - 可见: {is_visible}, 最小化: {is_minimized}, 最大化: {is_maximized}")

            # 多种策略尝试将窗口置于前台
            success = False

            # 策略1: 如果窗口被最小化，先恢复
            if is_minimized:
                result = user32.ShowWindow(hwnd, 9)  # SW_RESTORE
                self.log(f"恢复最小化窗口: {result}")

            # 策略2: 确保窗口可见
            show_result = user32.ShowWindow(hwnd, 5)  # SW_SHOW
            self.log(f"ShowWindow(SW_SHOW): {show_result}")

            # 策略3: 尝试AttachThreadInput技巧
            try:
                current_thread = kernel32.GetCurrentThreadId()
                target_thread = user32.GetWindowThreadProcessId(hwnd, None)
                self.log(f"线程ID - 当前: {current_thread}, 目标: {target_thread}")

                if target_thread != current_thread:
                    # 附加线程输入
                    attach_result = user32.AttachThreadInput(current_thread, target_thread, True)
                    self.log(f"AttachThreadInput: {attach_result}")

                    # 尝试设置前台窗口
                    if attach_result:
                        foreground_result = user32.SetForegroundWindow(hwnd)
                        self.log(f"SetForegroundWindow: {foreground_result}")
                        if foreground_result:
                            success = True

                        # 分离线程输入
                        detach_result = user32.AttachThreadInput(current_thread, target_thread, False)
                        self.log(f"DetachThreadInput: {detach_result}")
                    else:
                        # 直接尝试设置前台窗口
                        foreground_result = user32.SetForegroundWindow(hwnd)
                        self.log(f"SetForegroundWindow(直接): {foreground_result}")
                        if foreground_result:
                            success = True
                else:
                    # 同一线程，直接设置前台窗口
                    foreground_result = user32.SetForegroundWindow(hwnd)
                    self.log(f"SetForegroundWindow(同线程): {foreground_result}")
                    if foreground_result:
                        success = True

            except Exception as e:
                self.log(f"AttachThreadInput策略失败: {str(e)}")

            # 策略4: 使用 BringWindowToTop
            if not success:
                top_result = user32.BringWindowToTop(hwnd)
                self.log(f"BringWindowToTop: {top_result}")
                if top_result:
                    success = True

            # 策略5: 使用 SetWindowPos
            if not success:
                # HWND_TOP = 0, SWP_SHOWWINDOW = 0x0040, SWP_NOSIZE = 0x0001, SWP_NOMOVE = 0x0002
                SWP_SHOWWINDOW = 0x0040
                SWP_NOSIZE = 0x0001
                SWP_NOMOVE = 0x0002
                pos_result = user32.SetWindowPos(hwnd, 0, 0, 0, 0, 0, SWP_SHOWWINDOW | SWP_NOSIZE | SWP_NOMOVE)
                self.log(f"SetWindowPos: {pos_result}")
                if pos_result:
                    success = True

            # 策略6: 激活窗口
            try:
                active_result = user32.SetActiveWindow(hwnd)
                self.log(f"SetActiveWindow: {active_result}")
            except Exception as e:
                self.log(f"SetActiveWindow 失败: {str(e)}")

            # 最终检查窗口状态
            final_visible = user32.IsWindowVisible(hwnd)
            final_minimized = user32.IsIconic(hwnd)
            self.log(f"最终状态 - 可见: {final_visible}, 最小化: {final_minimized}")

            if success:
                self.log("✓ 窗口成功置于前台")
            else:
                self.log("⚠ 所有置前台策略都失败了，但窗口应该已经可见")
                # 如果窗口可见且不是最小化，认为成功
                if final_visible and not final_minimized:
                    success = True
                    self.log("✓ 窗口可见且未最小化，认为操作成功")

            return success

        except Exception as e:
            self.log(f"置于前台操作失败: {str(e)}")
            return False

    def find_window_by_pid_simple(self, target_pid: int, device_name: str):
        """通过PID查找窗口"""
        if not WIN_API_AVAILABLE:
            return None

        try:
            import ctypes
            user32 = ctypes.windll.user32

            found_hwnd = None

            def enum_windows_proc(hwnd, _):
                nonlocal found_hwnd
                try:
                    # 检查进程ID，不过滤可见性（因为最小化窗口也需要处理）
                    process_id = ctypes.c_ulong()
                    user32.GetWindowThreadProcessId(hwnd, ctypes.byref(process_id))

                    if process_id.value == target_pid:
                        length = user32.GetWindowTextLengthW(hwnd)
                        title = ""
                        if length > 0:
                            buffer = ctypes.create_unicode_buffer(length + 1)
                            user32.GetWindowTextW(hwnd, buffer, length + 1)
                            title = buffer.value

                        class_name = ctypes.create_unicode_buffer(256)
                        user32.GetClassNameW(hwnd, class_name, 256)

                        # 获取窗口状态用于调试
                        is_visible = user32.IsWindowVisible(hwnd)
                        is_minimized = user32.IsIconic(hwnd)

                        # 记录窗口信息用于调试
                        self.log(f"找到窗口 - PID: {process_id.value}, 标题: '{title}', 类名: '{class_name.value}', 可见: {is_visible}, 最小化: {is_minimized}")

                        # 更精确的匹配条件：
                        # 1. 类名为SDL_app且标题包含设备名称
                        # 2. 或者窗口标题完全等于设备名称
                        if ((class_name.value == "SDL_app" and device_name in title) or
                            device_name == title):
                            self.log(f"✓ 匹配成功 - HWND: {hwnd}")
                            found_hwnd = hwnd
                            return False

                except Exception:
                    pass
                return True

            EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
            user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)

            return found_hwnd

        except Exception as e:
            self.log(f"通过PID查找窗口失败: {str(e)}")
            return None

    # 批量操作功能
    def run_selected_devices(self):
        """运行选中的设备"""
        if not self.selected_devices:
            QMessageBox.warning(self, "警告", "请先选择要运行的设备")
            return

        selected_list = list(self.selected_devices)
        device_names = "、".join(selected_list)

        # 使用第一个设备的配置作为默认配置显示对话框
        first_device = next((d for d in self.devices if d['sb_name'] == selected_list[0]), None)
        if not first_device:
            QMessageBox.warning(self, "错误", "找不到设备配置")
            return

        # 显示运行任务配置对话框
        dialog = RunTaskDialog(first_device, parent=self)
        dialog.setWindowTitle(f"批量运行任务配置 - {len(selected_list)}个设备")

        # 修改对话框以显示多设备信息
        dialog.setup_multi_device_info(selected_list)

        if dialog.exec() != QDialog.DialogCode.Accepted:
            return

        # 获取用户选择的运行配置
        run_config = dialog.get_run_config()

        # 保存用户的选择作为下次的默认配置
        self.last_run_config = run_config.copy()

        # 确认运行
        reply = QMessageBox.question(
            self,
            "确认批量运行",
            f"确定要使用以下配置运行 {len(selected_list)} 个设备吗？\n\n"
            f"设备列表：{device_names}\n\n"
            f"运行配置：\n"
            f"- 开启应用：{'是' if run_config.get('need_open_app', False) else '否'}\n"
            f"- 分身模式：{'是' if run_config.get('is_clone', False) else '否'}\n"
            f"- 执行动作：{run_config.get('action', 'do_job')}",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.Yes
        )

        if reply != QMessageBox.StandardButton.Yes:
            return

        success_count = 0
        failed_devices = []

        for i, device_name in enumerate(selected_list):
            try:
                self.log(f"正在启动设备 {device_name} 的任务... ({i+1}/{len(selected_list)})")

                # 获取设备配置
                device = next((d for d in self.devices if d['sb_name'] == device_name), None)
                if not device:
                    failed_devices.append(device_name)
                    self.log(f"✗ 设备 {device_name} 不存在")
                    continue

                # 检查设备是否已在运行
                if device_name in self.running_processes:
                    process = self.running_processes[device_name]
                    if process.poll() is None:
                        self.log(f"设备 {device_name} 已在运行中，跳过")
                        continue
                    else:
                        del self.running_processes[device_name]

                # 确保设备有日志标签页
                self.ensure_device_log_tab(device_name)

                # 应用运行配置并启动
                self.run_device_with_config(device, run_config)

                # 添加延迟，确保进程完全启动后再继续下一个
                import time
                time.sleep(1.0)

                success_count += 1
                self.log(f"✓ 启动设备 {device_name} 的任务")

            except Exception as e:
                failed_devices.append(device_name)
                self.log(f"✗ 启动设备 {device_name} 任务失败: {str(e)}")

        # 显示结果
        if success_count == len(selected_list):
            QMessageBox.information(self, "运行完成", f"成功启动 {success_count} 个设备的任务")
        else:
            failed_list = "、".join(failed_devices)
            QMessageBox.warning(self, "部分运行失败",
                              f"成功启动 {success_count} 个设备任务\n启动失败：{failed_list}")

        self.log(f"批量运行完成：成功启动 {success_count}/{len(selected_list)} 个设备的任务")

    def run_device_with_config(self, device: dict, run_config: dict):
        """使用指定配置运行设备"""
        device_name = device.get('sb_name', '')

        # 保存原始配置
        original_config = {
            'need_open_app': device.get('need_open_app', False),
            'is_clone': device.get('is_clone', False),
            'action': device.get('action', 'do_job')
        }

        try:
            # 临时应用运行配置
            device.update(run_config)

            # 生成并执行命令
            command = self.generate_command(device)
            self.log(f"启动设备 {device_name}: {command}")

            # 设置环境变量
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'

            # 启动进程
            process = subprocess.Popen(
                command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                encoding='utf-8',
                errors='replace',
                cwd=os.getcwd(),
                env=env,
                bufsize=0
            )

            self.running_processes[device_name] = process
            device['task_running'] = True

            self.log(f"设备 {device_name} 启动成功 (PID: {process.pid})")
            self.log(f"运行配置: 开启应用={run_config['need_open_app']}, 分身模式={run_config['is_clone']}, 动作={run_config['action']}")

            # 启动监控线程
            threading.Thread(target=self.monitor_device_output, args=(device_name, process), daemon=True).start()

        except Exception as e:
            self.log(f"启动设备 {device_name} 失败: {str(e)}")
            raise
        finally:
            # 恢复原始配置
            device.update(original_config)

    def stop_selected_devices(self):
        """停止选中的设备"""
        if not self.selected_devices:
            QMessageBox.warning(self, "警告", "请先选择要停止的设备")
            return

        selected_list = list(self.selected_devices)
        device_names = "、".join(selected_list)

        reply = QMessageBox.question(
            self,
            "确认停止",
            f"确定要停止以下 {len(selected_list)} 个设备的任务吗？\n\n{device_names}",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.Yes
        )

        if reply != QMessageBox.StandardButton.Yes:
            return

        success_count = 0
        failed_devices = []

        for device_name in selected_list:
            try:
                self.stop_device(device_name)
                success_count += 1
                self.log(f"✓ 停止设备 {device_name} 的任务")
            except Exception as e:
                failed_devices.append(device_name)
                self.log(f"✗ 停止设备 {device_name} 任务失败: {str(e)}")

        # 显示结果
        if success_count == len(selected_list):
            QMessageBox.information(self, "停止完成", f"成功停止 {success_count} 个设备的任务")
        else:
            failed_list = "、".join(failed_devices)
            QMessageBox.warning(self, "部分停止失败",
                              f"成功停止 {success_count} 个设备任务\n停止失败：{failed_list}")

        self.log(f"批量停止完成：成功停止 {success_count}/{len(selected_list)} 个设备的任务")

    def open_selected_displays(self):
        """打开选中设备的显示"""
        if not self.selected_devices:
            QMessageBox.warning(self, "警告", "请先选择要打开显示的设备")
            return

        selected_list = list(self.selected_devices)
        device_names = "、".join(selected_list)

        reply = QMessageBox.question(
            self,
            "确认打开显示",
            f"确定要打开以下 {len(selected_list)} 个设备的显示吗？\n\n{device_names}",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.Yes
        )

        if reply != QMessageBox.StandardButton.Yes:
            return

        success_count = 0
        failed_devices = []

        for i, device_name in enumerate(selected_list):
            try:
                self.log(f"正在打开设备 {device_name} 的显示... ({i+1}/{len(selected_list)})")

                # 使用改进的双击逻辑，支持置于前台
                self.on_device_double_clicked(device_name)

                # 等待scrcpy进程完全启动并获取窗口句柄后再继续下一个
                # 这样可以避免窗口标题混乱的问题
                import time

                # 多次尝试验证窗口，最多等待6秒
                window_verified = False
                for attempt in range(3):
                    time.sleep(2.0)  # 每次等待2秒
                    if self.verify_device_window_created(device_name):
                        window_verified = True
                        break
                    else:
                        self.log(f"设备 {device_name} 窗口验证失败，重试 {attempt + 1}/3")

                if window_verified:
                    success_count += 1
                    self.log(f"✓ 设备 {device_name} 的显示已成功打开并验证")
                else:
                    self.log(f"⚠️ 设备 {device_name} 的显示窗口验证失败，但进程已启动")
                    success_count += 1  # 仍然计为成功，因为进程已启动

            except Exception as e:
                failed_devices.append(device_name)
                self.log(f"✗ 打开设备 {device_name} 显示失败: {str(e)}")
                import traceback
                self.log(f"错误详情: {traceback.format_exc()}")

        # 显示结果
        if success_count == len(selected_list):
            QMessageBox.information(self, "打开完成", f"成功打开 {success_count} 个设备的显示")
        else:
            failed_list = "、".join(failed_devices)
            QMessageBox.warning(self, "部分打开失败",
                              f"成功打开 {success_count} 个设备显示\n打开失败：{failed_list}")

        self.log(f"批量打开显示完成：成功打开 {success_count}/{len(selected_list)} 个设备的显示")

    def verify_device_window_created(self, device_name: str):
        """验证设备窗口是否正确创建"""
        try:
            if not WIN_API_AVAILABLE:
                return False

            import ctypes
            user32 = ctypes.windll.user32

            # 查找窗口标题匹配的scrcpy窗口
            found_correct_window = False

            def enum_windows_proc(hwnd, _):
                nonlocal found_correct_window
                try:
                    if user32.IsWindowVisible(hwnd):
                        length = user32.GetWindowTextLengthW(hwnd)
                        if length > 0:
                            buffer = ctypes.create_unicode_buffer(length + 1)
                            user32.GetWindowTextW(hwnd, buffer, length + 1)
                            title = buffer.value

                            class_name = ctypes.create_unicode_buffer(256)
                            user32.GetClassNameW(hwnd, class_name, 256)

                            # 检查是否为scrcpy窗口且标题匹配
                            if class_name.value == "SDL_app" and title == device_name:
                                found_correct_window = True
                                self.log(f"✓ 验证成功：找到设备 {device_name} 的正确窗口 (HWND: {hwnd})")
                                return False  # 停止枚举

                except Exception:
                    pass
                return True

            EnumWindowsProc = ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.c_void_p, ctypes.c_void_p)
            user32.EnumWindows(EnumWindowsProc(enum_windows_proc), 0)

            if not found_correct_window:
                self.log(f"⚠️ 验证失败：未找到设备 {device_name} 的正确窗口标题")

            return found_correct_window

        except Exception as e:
            self.log(f"验证设备 {device_name} 窗口时出错: {str(e)}")
            return False

    def close_selected_displays(self):
        """关闭选中设备的显示"""
        if not self.selected_devices:
            QMessageBox.warning(self, "警告", "请先选择要关闭显示的设备")
            return

        selected_list = list(self.selected_devices)
        device_names = "、".join(selected_list)

        reply = QMessageBox.question(
            self,
            "确认关闭显示",
            f"确定要关闭以下 {len(selected_list)} 个设备的显示吗？\n\n{device_names}",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.Yes
        )

        if reply != QMessageBox.StandardButton.Yes:
            return

        success_count = 0
        failed_devices = []

        for device_name in selected_list:
            try:
                self.close_device_display(device_name)
                success_count += 1
                self.log(f"✓ 关闭设备 {device_name} 的显示")
            except Exception as e:
                failed_devices.append(device_name)
                self.log(f"✗ 关闭设备 {device_name} 显示失败: {str(e)}")

        # 显示结果
        if success_count == len(selected_list):
            QMessageBox.information(self, "关闭完成", f"成功关闭 {success_count} 个设备的显示")
        else:
            failed_list = "、".join(failed_devices)
            QMessageBox.warning(self, "部分关闭失败",
                              f"成功关闭 {success_count} 个设备显示\n关闭失败：{failed_list}")

        self.log(f"批量关闭显示完成：成功关闭 {success_count}/{len(selected_list)} 个设备的显示")

    def delete_selected_devices(self):
        """删除选中的设备"""
        if not self.selected_devices:
            QMessageBox.warning(self, "警告", "请先选择要删除的设备")
            return

        selected_list = list(self.selected_devices)
        device_names = "、".join(selected_list)

        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除以下 {len(selected_list)} 个设备吗？\n\n{device_names}",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply != QMessageBox.StandardButton.Yes:
            return

        success_count = 0
        failed_devices = []

        # 创建要删除设备的副本，避免在迭代时修改集合
        devices_to_delete = selected_list.copy()

        for device_name in devices_to_delete:
            try:
                device = next((d for d in self.devices if d['sb_name'] == device_name), None)
                if device:
                    # 停止设备任务和关闭显示
                    self.stop_device(device_name)
                    self.close_device_display(device_name)

                    # 从列表中移除
                    self.devices.remove(device)
                    self.selected_devices.discard(device_name)

                    success_count += 1
                    self.log(f"✓ 删除设备: {device_name}")
                else:
                    failed_devices.append(device_name)
                    self.log(f"✗ 设备 {device_name} 不存在")

            except Exception as e:
                failed_devices.append(device_name)
                self.log(f"✗ 删除设备 {device_name} 失败: {str(e)}")

        # 更新界面
        self.render_devices()
        self.save_config()

        # 显示结果
        if success_count == len(devices_to_delete):
            QMessageBox.information(self, "删除完成", f"成功删除 {success_count} 个设备")
        else:
            failed_list = "、".join(failed_devices)
            QMessageBox.warning(self, "部分删除失败",
                              f"成功删除 {success_count} 个设备\n删除失败：{failed_list}")

        self.log(f"批量删除完成：成功删除 {success_count}/{len(devices_to_delete)} 个设备")

    def select_all_devices(self):
        """全选设备"""
        self.selected_devices = {device['sb_name'] for device in self.devices}
        self.update_selection_display()

    def deselect_all_devices(self):
        """取消选择所有设备"""
        self.selected_devices.clear()
        self.update_selection_display()



    # 菜单功能
    def import_config(self):
        """导入配置"""
        from PyQt6.QtWidgets import QFileDialog

        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入配置文件", "", "JSON文件 (*.json)"
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.devices = data.get('devices', [])
                    self.convert_legacy_data()
                    self.render_devices()
                    self.log(f"从 {file_path} 导入配置成功")
                    QMessageBox.information(self, "成功", "配置导入成功")
            except Exception as e:
                self.log(f"导入配置失败: {str(e)}")
                QMessageBox.warning(self, "错误", f"导入配置失败: {str(e)}")

    def export_config(self):
        """导出配置"""
        from PyQt6.QtWidgets import QFileDialog

        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出配置文件", "device_config.json", "JSON文件 (*.json)"
        )

        if file_path:
            try:
                config_data = {'devices': self.devices}
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(config_data, f, ensure_ascii=False, indent=4)
                self.log(f"配置导出到 {file_path} 成功")
                QMessageBox.information(self, "成功", "配置导出成功")
            except Exception as e:
                self.log(f"导出配置失败: {str(e)}")
                QMessageBox.warning(self, "错误", f"导出配置失败: {str(e)}")

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于", """
设备管理程序 - PyQt6版本

功能特点：
• 1:1复刻Web版本功能
• 优化的性能和用户体验
• 设备管理和ADB连接
• scrcpy显示集成
• 智能窗口排列
• 批量操作支持

版本: 1.0.0
基于: PyQt6
        """.strip())


class PhoneCardEditDialog(QDialog):
    """手机卡编辑对话框"""

    def __init__(self, main_window, card_data=None, card_id=None, parent=None):
        super().__init__(parent)
        self.main_window = main_window
        self.card_data = card_data or {}
        self.card_id = card_id
        self.is_edit_mode = card_id is not None
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """设置UI"""
        title = "编辑手机卡" if self.is_edit_mode else "添加手机卡"
        self.setWindowTitle(title)
        self.setFixedSize(500, 400)
        self.setModal(True)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 表单布局
        form_layout = QFormLayout()
        form_layout.setSpacing(10)

        # 手机号码
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("请输入手机号码")
        form_layout.addRow("手机号码:", self.phone_edit)

        # 绑定设备
        self.device_combo = QComboBox()
        self.device_combo.addItem("未绑定", "")
        device_names = self.main_window.get_device_names_list()
        for device_name in device_names:
            self.device_combo.addItem(device_name, device_name)
        form_layout.addRow("绑定设备:", self.device_combo)

        layout.addLayout(form_layout)

        # 标签管理区域
        tags_label = QLabel("自定义标签:")
        tags_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
        layout.addWidget(tags_label)

        # 标签容器
        self.tags_widget = QWidget()
        self.tags_layout = QVBoxLayout(self.tags_widget)
        self.tags_layout.setContentsMargins(0, 0, 0, 0)
        self.tags_layout.setSpacing(5)

        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidget(self.tags_widget)
        scroll_area.setWidgetResizable(True)
        scroll_area.setMaximumHeight(150)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: 1px solid #dee2e6;
                border-radius: 4px;
                background: white;
            }
        """)
        layout.addWidget(scroll_area)

        # 添加标签按钮
        add_tag_btn = QPushButton("➕ 添加标签")
        add_tag_btn.clicked.connect(self.add_tag_field)
        layout.addWidget(add_tag_btn)

        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)

        self.save_btn = QPushButton("保存")
        self.save_btn.clicked.connect(self.save_card)
        self.save_btn.setDefault(True)

        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.save_btn)

        layout.addLayout(button_layout)

        # 设置样式
        self.setStyleSheet("""
            QDialog {
                background: white;
            }
            QLineEdit, QComboBox {
                padding: 8px;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-size: 13px;
            }
            QLineEdit:focus, QComboBox:focus {
                border-color: #007bff;
            }
            QPushButton {
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                font-size: 13px;
                font-weight: 500;
                min-width: 80px;
            }
            QPushButton#save_btn {
                background: #007bff;
                color: white;
            }
            QPushButton#save_btn:hover {
                background: #0056b3;
            }
            QPushButton#cancel_btn {
                background: #6c757d;
                color: white;
            }
            QPushButton#cancel_btn:hover {
                background: #5a6268;
            }
        """)

        self.save_btn.setObjectName("save_btn")
        self.cancel_btn.setObjectName("cancel_btn")

        # 初始化标签字段
        self.tag_fields = []
        self.add_tag_field()  # 默认添加一个标签字段

    def add_tag_field(self):
        """添加标签字段"""
        tag_widget = QWidget()
        tag_layout = QHBoxLayout(tag_widget)
        tag_layout.setContentsMargins(0, 0, 0, 0)
        tag_layout.setSpacing(5)

        # 标签名
        name_edit = QLineEdit()
        name_edit.setPlaceholderText("标签名（如：实名）")
        tag_layout.addWidget(name_edit)

        # 标签值
        value_edit = QLineEdit()
        value_edit.setPlaceholderText("标签值（如：张三）")
        tag_layout.addWidget(value_edit)

        # 删除按钮
        delete_btn = QPushButton("🗑️")
        delete_btn.setFixedSize(30, 30)
        delete_btn.clicked.connect(lambda: self.remove_tag_field(tag_widget))
        tag_layout.addWidget(delete_btn)

        self.tags_layout.addWidget(tag_widget)
        self.tag_fields.append((name_edit, value_edit, tag_widget))

    def remove_tag_field(self, tag_widget):
        """移除标签字段"""
        # 至少保留一个标签字段
        if len(self.tag_fields) <= 1:
            return

        # 找到并移除对应的字段
        for i, (name_edit, value_edit, widget) in enumerate(self.tag_fields):
            if widget == tag_widget:
                widget.setParent(None)
                del self.tag_fields[i]
                break

    def load_data(self):
        """加载数据"""
        if not self.card_data:
            return

        # 加载手机号码
        self.phone_edit.setText(self.card_data.get('phone_number', ''))

        # 加载绑定设备
        device_name = self.card_data.get('device_name', '')
        index = self.device_combo.findData(device_name)
        if index >= 0:
            self.device_combo.setCurrentIndex(index)

        # 加载标签
        tags = self.card_data.get('tags', {})
        if tags:
            # 清除默认的空标签字段
            for name_edit, value_edit, widget in self.tag_fields:
                widget.setParent(None)
            self.tag_fields.clear()

            # 添加现有标签
            for tag_name, tag_value in tags.items():
                self.add_tag_field()
                name_edit, value_edit, _ = self.tag_fields[-1]
                name_edit.setText(tag_name)
                value_edit.setText(tag_value)

    def save_card(self):
        """保存手机卡"""
        phone_number = self.phone_edit.text().strip()
        if not phone_number:
            QMessageBox.warning(self, "警告", "请输入手机号码！")
            return

        device_name = self.device_combo.currentData()

        # 收集标签
        tags = {}
        for name_edit, value_edit, _ in self.tag_fields:
            tag_name = name_edit.text().strip()
            tag_value = value_edit.text().strip()
            if tag_name and tag_value:
                tags[tag_name] = tag_value

        # 创建卡数据
        from datetime import datetime
        current_time = datetime.now().isoformat()

        card_data = {
            'phone_number': phone_number,
            'device_name': device_name,
            'tags': tags,
            'updated_time': current_time
        }

        if not self.is_edit_mode:
            card_data['created_time'] = current_time

        # 保存数据
        if self.is_edit_mode:
            self.main_window.phone_cards[self.card_id] = card_data
        else:
            card_id = self.main_window.generate_card_id()
            self.main_window.phone_cards[card_id] = card_data

        self.main_window.save_config()

        action = "更新" if self.is_edit_mode else "添加"
        if hasattr(self.main_window, 'log'):
            self.main_window.log(f"{action}手机卡: {phone_number}")

        self.accept()


class PhoneCardManagerWidget(QWidget):
    """手机卡号管理组件"""

    def __init__(self, main_window, parent=None):
        super().__init__(parent)
        self.main_window = main_window
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # 顶部操作按钮
        button_layout = QHBoxLayout()

        self.add_btn = QPushButton("➕ 添加手机卡")
        self.add_btn.clicked.connect(self.add_phone_card)
        button_layout.addWidget(self.add_btn)

        self.edit_btn = QPushButton("✏️ 编辑")
        self.edit_btn.clicked.connect(self.edit_phone_card)
        self.edit_btn.setEnabled(False)
        button_layout.addWidget(self.edit_btn)

        self.delete_btn = QPushButton("🗑️ 删除")
        self.delete_btn.clicked.connect(self.delete_phone_card)
        self.delete_btn.setEnabled(False)
        button_layout.addWidget(self.delete_btn)

        self.edit_apps_btn = QPushButton("📱 编辑关联APP")
        self.edit_apps_btn.clicked.connect(self.edit_related_apps)
        self.edit_apps_btn.setEnabled(False)
        button_layout.addWidget(self.edit_apps_btn)

        button_layout.addStretch()

        self.refresh_btn = QPushButton("🔄 刷新")
        self.refresh_btn.clicked.connect(self.load_data)
        button_layout.addWidget(self.refresh_btn)

        layout.addLayout(button_layout)

        # 筛选区域
        filter_layout = QHBoxLayout()

        # 设备筛选
        filter_layout.addWidget(QLabel("设备筛选:"))
        self.device_filter_combo = QComboBox()
        self.device_filter_combo.addItem("全部", "")
        device_names = self.main_window.get_device_names_list()
        for device_name in device_names:
            self.device_filter_combo.addItem(device_name, device_name)
        self.device_filter_combo.currentTextChanged.connect(self.apply_filters)
        filter_layout.addWidget(self.device_filter_combo)

        # 清除筛选按钮
        clear_filter_btn = QPushButton("🔄 清除筛选")
        clear_filter_btn.clicked.connect(self.clear_filters)
        filter_layout.addWidget(clear_filter_btn)

        filter_layout.addStretch()
        layout.addLayout(filter_layout)

        # 手机卡列表表格
        self.table = QTableWidget()
        self.table.setColumnCount(5)
        self.table.setHorizontalHeaderLabels(["手机号码", "绑定设备", "标签", "更新时间", "关联APP"])

        # 设置表格属性
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.horizontalHeader().setStretchLastSection(True)
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)

        # 连接选择变化信号
        self.table.itemSelectionChanged.connect(self.on_selection_changed)

        layout.addWidget(self.table)

        # 设置样式
        self.setStyleSheet("""
            QPushButton {
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                font-size: 13px;
                font-weight: 500;
                min-width: 80px;
            }
            QPushButton:enabled {
                background: #007bff;
                color: white;
            }
            QPushButton:enabled:hover {
                background: #0056b3;
            }
            QPushButton:disabled {
                background: #6c757d;
                color: #adb5bd;
            }
            QTableWidget {
                border: 1px solid #dee2e6;
                border-radius: 4px;
                background: white;
                gridline-color: #dee2e6;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background: #007bff;
                color: white;
            }
            QHeaderView::section {
                background: #f8f9fa;
                padding: 8px;
                border: none;
                border-bottom: 2px solid #dee2e6;
                font-weight: bold;
            }
        """)

    def load_data(self):
        """加载手机卡数据"""
        try:
            phone_cards = self.main_window.phone_cards

            # 应用设备筛选条件
            device_filter = self.device_filter_combo.currentData() if hasattr(self, 'device_filter_combo') else ""

            # 记录筛选信息
            if hasattr(self.main_window, 'log'):
                self.main_window.log(f"手机卡管理：加载数据，设备筛选='{device_filter}'，总卡数={len(phone_cards)}")

            # 筛选数据
            filtered_cards = {}
            for card_id, card_data in phone_cards.items():
                # 设备筛选
                if device_filter and card_data.get('device_name', '') != device_filter:
                    continue
                filtered_cards[card_id] = card_data

            # 记录筛选结果
            if hasattr(self.main_window, 'log'):
                self.main_window.log(f"手机卡管理：筛选后卡数={len(filtered_cards)}")

            self.table.setRowCount(len(filtered_cards))

            for row, (card_id, card_data) in enumerate(filtered_cards.items()):
                # 手机号码
                phone_item = QTableWidgetItem(card_data.get('phone_number', ''))
                phone_item.setData(Qt.ItemDataRole.UserRole, card_id)
                self.table.setItem(row, 0, phone_item)

                # 绑定设备
                device_item = QTableWidgetItem(card_data.get('device_name', ''))
                self.table.setItem(row, 1, device_item)

                # 标签
                tags = card_data.get('tags', {})
                tag_text = ', '.join([f"{k}:{v}" for k, v in tags.items()])
                tag_item = QTableWidgetItem(tag_text)
                self.table.setItem(row, 2, tag_item)

                # 更新时间
                update_time = card_data.get('updated_time', '')
                if update_time:
                    try:
                        from datetime import datetime
                        dt = datetime.fromisoformat(update_time.replace('Z', '+00:00'))
                        time_text = dt.strftime('%Y-%m-%d %H:%M')
                    except:
                        time_text = update_time
                else:
                    time_text = ''
                time_item = QTableWidgetItem(time_text)
                self.table.setItem(row, 3, time_item)

                # 关联APP信息
                phone_number = card_data.get('phone_number', '')
                related_apps = self.get_related_apps(phone_number)
                app_text = ', '.join(related_apps) if related_apps else '无关联APP'
                app_item = QTableWidgetItem(app_text)
                self.table.setItem(row, 4, app_item)

        except Exception as e:
            if hasattr(self.main_window, 'log'):
                self.main_window.log(f"加载手机卡数据失败: {str(e)}")

    def apply_filters(self):
        """应用筛选条件"""
        self.load_data()

    def clear_filters(self):
        """清除筛选条件"""
        if hasattr(self, 'device_filter_combo'):
            self.device_filter_combo.setCurrentIndex(0)
        self.load_data()

    def set_device_filter(self, device_name):
        """设置设备筛选"""
        if hasattr(self, 'device_filter_combo'):
            index = self.device_filter_combo.findData(device_name)
            if index >= 0:
                self.device_filter_combo.setCurrentIndex(index)
            else:
                # 如果设备不在列表中，添加它
                self.device_filter_combo.addItem(device_name, device_name)
                # 设置为新添加的项目
                new_index = self.device_filter_combo.count() - 1
                self.device_filter_combo.setCurrentIndex(new_index)

            # 记录筛选操作
            if hasattr(self.main_window, 'log'):
                self.main_window.log(f"手机卡管理：设置设备筛选为 '{device_name}'")

            self.apply_filters()

    def get_related_apps(self, phone_number):
        """获取手机号关联的APP列表"""
        try:
            related_apps = []
            app_accounts = self.main_window.app_accounts
            for account_data in app_accounts.values():
                if account_data.get('phone_number') == phone_number:
                    app_name = account_data.get('app_name', '未知')
                    app_type = account_data.get('app_type', 'original')
                    device_name = account_data.get('device_name', '未知设备')
                    type_text = "原" if app_type == "original" else "分身"
                    related_apps.append(f"{app_name}({type_text}) - {device_name}")
            return related_apps
        except Exception as e:
            return []

    def on_selection_changed(self):
        """选择变化事件"""
        has_selection = len(self.table.selectedItems()) > 0
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)
        self.edit_apps_btn.setEnabled(has_selection)

    def add_phone_card(self):
        """添加手机卡"""
        dialog = PhoneCardEditDialog(self.main_window, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.load_data()

    def edit_phone_card(self):
        """编辑手机卡"""
        current_row = self.table.currentRow()
        if current_row >= 0:
            card_id = self.table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)
            card_data = self.main_window.phone_cards.get(card_id, {})
            dialog = PhoneCardEditDialog(self.main_window, card_data, card_id, parent=self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.load_data()

    def delete_phone_card(self):
        """删除手机卡"""
        current_row = self.table.currentRow()
        if current_row >= 0:
            card_id = self.table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)
            phone_number = self.table.item(current_row, 0).text()

            reply = QMessageBox.question(self, "确认删除",
                                       f"确定要删除手机卡 {phone_number} 吗？",
                                       QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

            if reply == QMessageBox.StandardButton.Yes:
                if card_id in self.main_window.phone_cards:
                    del self.main_window.phone_cards[card_id]
                    self.main_window.save_config()
                    self.load_data()
                    if hasattr(self.main_window, 'log'):
                        self.main_window.log(f"已删除手机卡: {phone_number}")

    def edit_related_apps(self):
        """编辑关联APP"""
        current_row = self.table.currentRow()
        if current_row >= 0:
            card_id = self.table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)
            card_data = self.main_window.phone_cards.get(card_id, {})
            phone_number = card_data.get('phone_number', '')

            if phone_number:
                # 通过主窗口找到卡账管理对话框
                for widget in QApplication.allWidgets():
                    if isinstance(widget, CardAccountManagerDialog):
                        widget.switch_to_app_tab_with_filter(phone_number)
                        return

                # 如果找不到对话框，记录错误
                if hasattr(self.main_window, 'log'):
                    self.main_window.log("无法找到卡账管理对话框")


class AppAccountEditDialog(QDialog):
    """APP账号编辑对话框"""

    def __init__(self, main_window, account_data=None, account_id=None, parent=None):
        super().__init__(parent)
        self.main_window = main_window
        self.account_data = account_data or {}
        self.account_id = account_id
        self.is_edit_mode = account_id is not None
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """设置UI"""
        title = "编辑APP账号" if self.is_edit_mode else "添加APP账号"
        self.setWindowTitle(title)
        self.setFixedSize(500, 450)
        self.setModal(True)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 表单布局
        form_layout = QFormLayout()
        form_layout.setSpacing(10)

        # APP名称
        self.app_combo = QComboBox()
        self.app_combo.setEditable(True)
        common_apps = ["淘宝", "京东", "拼多多", "天猫", "支付宝", "微信", "抖音", "快手"]
        self.app_combo.addItems(common_apps)
        self.app_combo.setCurrentText("")
        form_layout.addRow("APP名称:", self.app_combo)

        # 绑定手机号码
        self.phone_combo = QComboBox()
        self.phone_combo.addItem("未绑定", "")
        self.load_phone_numbers()
        form_layout.addRow("绑定手机号码:", self.phone_combo)

        # 绑定设备
        self.device_combo = QComboBox()
        self.device_combo.addItem("未绑定", "")
        device_names = self.main_window.get_device_names_list()
        for device_name in device_names:
            self.device_combo.addItem(device_name, device_name)
        form_layout.addRow("绑定设备:", self.device_combo)

        # 应用类型
        self.app_type_combo = QComboBox()
        self.app_type_combo.addItem("原应用", "original")
        self.app_type_combo.addItem("分身应用", "clone")
        form_layout.addRow("应用类型:", self.app_type_combo)

        layout.addLayout(form_layout)

        # 标签管理区域
        tags_label = QLabel("自定义标签:")
        tags_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
        layout.addWidget(tags_label)

        # 标签容器
        self.tags_widget = QWidget()
        self.tags_layout = QVBoxLayout(self.tags_widget)
        self.tags_layout.setContentsMargins(0, 0, 0, 0)
        self.tags_layout.setSpacing(5)

        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidget(self.tags_widget)
        scroll_area.setWidgetResizable(True)
        scroll_area.setMaximumHeight(150)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: 1px solid #dee2e6;
                border-radius: 4px;
                background: white;
            }
        """)
        layout.addWidget(scroll_area)

        # 添加标签按钮
        add_tag_btn = QPushButton("➕ 添加标签")
        add_tag_btn.clicked.connect(self.add_tag_field)
        layout.addWidget(add_tag_btn)

        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)

        self.save_btn = QPushButton("保存")
        self.save_btn.clicked.connect(self.save_account)
        self.save_btn.setDefault(True)

        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.save_btn)

        layout.addLayout(button_layout)

        # 设置样式
        self.setStyleSheet("""
            QDialog {
                background: white;
            }
            QLineEdit, QComboBox {
                padding: 8px;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-size: 13px;
            }
            QLineEdit:focus, QComboBox:focus {
                border-color: #007bff;
            }
            QPushButton {
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                font-size: 13px;
                font-weight: 500;
                min-width: 80px;
            }
            QPushButton#save_btn {
                background: #007bff;
                color: white;
            }
            QPushButton#save_btn:hover {
                background: #0056b3;
            }
            QPushButton#cancel_btn {
                background: #6c757d;
                color: white;
            }
            QPushButton#cancel_btn:hover {
                background: #5a6268;
            }
        """)

        self.save_btn.setObjectName("save_btn")
        self.cancel_btn.setObjectName("cancel_btn")

        # 初始化标签字段
        self.tag_fields = []
        self.add_tag_field()  # 默认添加一个标签字段

    def load_phone_numbers(self):
        """加载手机号码列表"""
        try:
            phone_cards = self.main_window.phone_cards
            for card_id, card_data in phone_cards.items():
                phone_number = card_data.get('phone_number', '')
                if phone_number:
                    # 显示格式：手机号码 (绑定设备)
                    device_name = card_data.get('device_name', '未绑定设备')
                    display_text = f"{phone_number} ({device_name})"
                    self.phone_combo.addItem(display_text, phone_number)
        except Exception as e:
            print(f"加载手机号码失败: {str(e)}")

    def add_tag_field(self):
        """添加标签字段"""
        tag_widget = QWidget()
        tag_layout = QHBoxLayout(tag_widget)
        tag_layout.setContentsMargins(0, 0, 0, 0)
        tag_layout.setSpacing(5)

        # 标签名
        name_edit = QLineEdit()
        name_edit.setPlaceholderText("标签名（如：实名）")
        tag_layout.addWidget(name_edit)

        # 标签值
        value_edit = QLineEdit()
        value_edit.setPlaceholderText("标签值（如：是）")
        tag_layout.addWidget(value_edit)

        # 删除按钮
        delete_btn = QPushButton("🗑️")
        delete_btn.setFixedSize(30, 30)
        delete_btn.clicked.connect(lambda: self.remove_tag_field(tag_widget))
        tag_layout.addWidget(delete_btn)

        self.tags_layout.addWidget(tag_widget)
        self.tag_fields.append((name_edit, value_edit, tag_widget))

    def remove_tag_field(self, tag_widget):
        """移除标签字段"""
        # 至少保留一个标签字段
        if len(self.tag_fields) <= 1:
            return

        # 找到并移除对应的字段
        for i, (name_edit, value_edit, widget) in enumerate(self.tag_fields):
            if widget == tag_widget:
                widget.setParent(None)
                del self.tag_fields[i]
                break

    def load_data(self):
        """加载数据"""
        if not self.account_data:
            return

        # 加载APP名称
        self.app_combo.setCurrentText(self.account_data.get('app_name', ''))

        # 加载绑定手机号码
        phone_number = self.account_data.get('phone_number', '')
        if phone_number:
            index = self.phone_combo.findData(phone_number)
            if index >= 0:
                self.phone_combo.setCurrentIndex(index)

        # 加载绑定设备
        device_name = self.account_data.get('device_name', '')
        index = self.device_combo.findData(device_name)
        if index >= 0:
            self.device_combo.setCurrentIndex(index)

        # 加载应用类型
        app_type = self.account_data.get('app_type', 'original')
        index = self.app_type_combo.findData(app_type)
        if index >= 0:
            self.app_type_combo.setCurrentIndex(index)

        # 加载标签
        tags = self.account_data.get('tags', {})
        if tags:
            # 清除默认的空标签字段
            for name_edit, value_edit, widget in self.tag_fields:
                widget.setParent(None)
            self.tag_fields.clear()

            # 添加现有标签
            for tag_name, tag_value in tags.items():
                self.add_tag_field()
                name_edit, value_edit, _ = self.tag_fields[-1]
                name_edit.setText(tag_name)
                value_edit.setText(tag_value)

    def save_account(self):
        """保存APP账号"""
        app_name = self.app_combo.currentText().strip()
        if not app_name:
            QMessageBox.warning(self, "警告", "请输入APP名称！")
            return

        phone_number = self.phone_combo.currentData()
        device_name = self.device_combo.currentData()
        app_type = self.app_type_combo.currentData()

        # 收集标签
        tags = {}
        for name_edit, value_edit, _ in self.tag_fields:
            tag_name = name_edit.text().strip()
            tag_value = value_edit.text().strip()
            if tag_name and tag_value:
                tags[tag_name] = tag_value

        # 创建账号数据
        from datetime import datetime
        current_time = datetime.now().isoformat()

        account_data = {
            'app_name': app_name,
            'phone_number': phone_number,
            'device_name': device_name,
            'app_type': app_type,
            'tags': tags,
            'updated_time': current_time
        }

        if not self.is_edit_mode:
            account_data['created_time'] = current_time

        # 保存数据
        if self.is_edit_mode:
            self.main_window.app_accounts[self.account_id] = account_data
        else:
            account_id = self.main_window.generate_account_id()
            self.main_window.app_accounts[account_id] = account_data

        self.main_window.save_config()

        action = "更新" if self.is_edit_mode else "添加"
        if hasattr(self.main_window, 'log'):
            self.main_window.log(f"{action}APP账号: {app_name}")

        self.accept()


class AppAccountManagerWidget(QWidget):
    """APP账号管理组件"""

    def __init__(self, main_window, parent=None):
        super().__init__(parent)
        self.main_window = main_window
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # 顶部操作按钮
        button_layout = QHBoxLayout()

        self.add_btn = QPushButton("➕ 添加APP账号")
        self.add_btn.clicked.connect(self.add_app_account)
        button_layout.addWidget(self.add_btn)

        self.edit_btn = QPushButton("✏️ 编辑")
        self.edit_btn.clicked.connect(self.edit_app_account)
        self.edit_btn.setEnabled(False)
        button_layout.addWidget(self.edit_btn)

        self.delete_btn = QPushButton("🗑️ 删除")
        self.delete_btn.clicked.connect(self.delete_app_account)
        self.delete_btn.setEnabled(False)
        button_layout.addWidget(self.delete_btn)

        button_layout.addStretch()

        self.refresh_btn = QPushButton("🔄 刷新")
        self.refresh_btn.clicked.connect(self.load_data)
        button_layout.addWidget(self.refresh_btn)

        layout.addLayout(button_layout)

        # 筛选区域
        filter_layout = QHBoxLayout()

        # 手机号筛选
        filter_layout.addWidget(QLabel("手机号筛选:"))
        self.phone_filter_combo = QComboBox()
        self.phone_filter_combo.addItem("全部", "")
        self.load_phone_filter_options()
        self.phone_filter_combo.currentTextChanged.connect(self.apply_filters)
        filter_layout.addWidget(self.phone_filter_combo)

        # 设备筛选
        filter_layout.addWidget(QLabel("设备筛选:"))
        self.device_filter_combo = QComboBox()
        self.device_filter_combo.addItem("全部", "")
        device_names = self.main_window.get_device_names_list()
        for device_name in device_names:
            self.device_filter_combo.addItem(device_name, device_name)
        self.device_filter_combo.currentTextChanged.connect(self.apply_filters)
        filter_layout.addWidget(self.device_filter_combo)

        # 清除筛选按钮
        clear_filter_btn = QPushButton("🔄 清除筛选")
        clear_filter_btn.clicked.connect(self.clear_filters)
        filter_layout.addWidget(clear_filter_btn)

        filter_layout.addStretch()
        layout.addLayout(filter_layout)

        # APP账号列表表格
        self.table = QTableWidget()
        self.table.setColumnCount(7)
        self.table.setHorizontalHeaderLabels(["APP名称", "绑定手机号", "绑定设备", "应用类型", "标签", "更新时间", "操作"])

        # 设置表格属性
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.horizontalHeader().setStretchLastSection(True)
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)

        # 连接选择变化信号
        self.table.itemSelectionChanged.connect(self.on_selection_changed)

        layout.addWidget(self.table)

        # 设置样式
        self.setStyleSheet("""
            QPushButton {
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                font-size: 13px;
                font-weight: 500;
                min-width: 80px;
            }
            QPushButton:enabled {
                background: #007bff;
                color: white;
            }
            QPushButton:enabled:hover {
                background: #0056b3;
            }
            QPushButton:disabled {
                background: #6c757d;
                color: #adb5bd;
            }
            QTableWidget {
                border: 1px solid #dee2e6;
                border-radius: 4px;
                background: white;
                gridline-color: #dee2e6;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background: #007bff;
                color: white;
            }
            QHeaderView::section {
                background: #f8f9fa;
                padding: 8px;
                border: none;
                border-bottom: 2px solid #dee2e6;
                font-weight: bold;
            }
        """)

    def load_data(self):
        """加载APP账号数据"""
        try:
            app_accounts = self.main_window.app_accounts

            # 应用筛选条件
            phone_filter = self.phone_filter_combo.currentData() if hasattr(self, 'phone_filter_combo') else ""
            device_filter = self.device_filter_combo.currentData() if hasattr(self, 'device_filter_combo') else ""

            # 记录筛选信息
            if hasattr(self.main_window, 'log'):
                self.main_window.log(f"APP账号管理：加载数据，手机号筛选='{phone_filter}'，设备筛选='{device_filter}'，总账号数={len(app_accounts)}")

            # 筛选数据
            filtered_accounts = {}
            for account_id, account_data in app_accounts.items():
                # 手机号筛选
                if phone_filter and account_data.get('phone_number', '') != phone_filter:
                    continue
                # 设备筛选
                if device_filter and account_data.get('device_name', '') != device_filter:
                    continue
                filtered_accounts[account_id] = account_data

            # 记录筛选结果
            if hasattr(self.main_window, 'log'):
                self.main_window.log(f"APP账号管理：筛选后账号数={len(filtered_accounts)}")

            self.table.setRowCount(len(filtered_accounts))

            for row, (account_id, account_data) in enumerate(filtered_accounts.items()):
                # APP名称
                app_item = QTableWidgetItem(account_data.get('app_name', ''))
                app_item.setData(Qt.ItemDataRole.UserRole, account_id)
                self.table.setItem(row, 0, app_item)

                # 绑定手机号
                phone_item = QTableWidgetItem(account_data.get('phone_number', ''))
                self.table.setItem(row, 1, phone_item)

                # 绑定设备
                device_item = QTableWidgetItem(account_data.get('device_name', ''))
                self.table.setItem(row, 2, device_item)

                # 应用类型
                app_type = account_data.get('app_type', 'original')
                type_text = "原应用" if app_type == "original" else "分身应用"
                type_item = QTableWidgetItem(type_text)
                self.table.setItem(row, 3, type_item)

                # 标签
                tags = account_data.get('tags', {})
                tag_text = ', '.join([f"{k}:{v}" for k, v in tags.items()])
                tag_item = QTableWidgetItem(tag_text)
                self.table.setItem(row, 4, tag_item)

                # 更新时间
                update_time = account_data.get('updated_time', '')
                if update_time:
                    try:
                        from datetime import datetime
                        dt = datetime.fromisoformat(update_time.replace('Z', '+00:00'))
                        time_text = dt.strftime('%Y-%m-%d %H:%M')
                    except:
                        time_text = update_time
                else:
                    time_text = ''
                time_item = QTableWidgetItem(time_text)
                self.table.setItem(row, 5, time_item)

                # 操作按钮
                action_item = QTableWidgetItem("编辑/删除")
                self.table.setItem(row, 6, action_item)

        except Exception as e:
            if hasattr(self.main_window, 'log'):
                self.main_window.log(f"加载APP账号数据失败: {str(e)}")

    def load_phone_filter_options(self):
        """加载手机号筛选选项"""
        try:
            phone_cards = self.main_window.phone_cards
            for card_data in phone_cards.values():
                phone_number = card_data.get('phone_number', '')
                if phone_number:
                    self.phone_filter_combo.addItem(phone_number, phone_number)
        except Exception as e:
            pass

    def apply_filters(self):
        """应用筛选条件"""
        self.load_data()

    def clear_filters(self):
        """清除筛选条件"""
        self.phone_filter_combo.setCurrentIndex(0)
        self.device_filter_combo.setCurrentIndex(0)
        self.load_data()

    def set_phone_filter(self, phone_number):
        """设置手机号筛选"""
        index = self.phone_filter_combo.findData(phone_number)
        if index >= 0:
            self.phone_filter_combo.setCurrentIndex(index)
        else:
            # 如果手机号不在列表中，添加它
            self.phone_filter_combo.addItem(phone_number, phone_number)
            # 设置为新添加的项目
            new_index = self.phone_filter_combo.count() - 1
            self.phone_filter_combo.setCurrentIndex(new_index)

        # 记录筛选操作
        if hasattr(self.main_window, 'log'):
            self.main_window.log(f"APP账号管理：设置手机号筛选为 '{phone_number}'")

        self.apply_filters()

    def set_device_filter(self, device_name):
        """设置设备筛选"""
        index = self.device_filter_combo.findData(device_name)
        if index >= 0:
            self.device_filter_combo.setCurrentIndex(index)
        else:
            # 如果设备不在列表中，添加它
            self.device_filter_combo.addItem(device_name, device_name)
            # 设置为新添加的项目
            new_index = self.device_filter_combo.count() - 1
            self.device_filter_combo.setCurrentIndex(new_index)

        # 记录筛选操作
        if hasattr(self.main_window, 'log'):
            self.main_window.log(f"APP账号管理：设置设备筛选为 '{device_name}'")

        self.apply_filters()

    def on_selection_changed(self):
        """选择变化事件"""
        has_selection = len(self.table.selectedItems()) > 0
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)

    def add_app_account(self):
        """添加APP账号"""
        dialog = AppAccountEditDialog(self.main_window, parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.load_data()

    def edit_app_account(self):
        """编辑APP账号"""
        current_row = self.table.currentRow()
        if current_row >= 0:
            account_id = self.table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)
            account_data = self.main_window.app_accounts.get(account_id, {})
            dialog = AppAccountEditDialog(self.main_window, account_data, account_id, parent=self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.load_data()

    def delete_app_account(self):
        """删除APP账号"""
        current_row = self.table.currentRow()
        if current_row >= 0:
            account_id = self.table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)
            app_name = self.table.item(current_row, 0).text()

            reply = QMessageBox.question(self, "确认删除",
                                       f"确定要删除APP账号 {app_name} 吗？",
                                       QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

            if reply == QMessageBox.StandardButton.Yes:
                if account_id in self.main_window.app_accounts:
                    del self.main_window.app_accounts[account_id]
                    self.main_window.save_config()
                    self.load_data()
                    if hasattr(self.main_window, 'log'):
                        self.main_window.log(f"已删除APP账号: {app_name}")


class CardAccountManagerDialog(QDialog):
    """卡账管理主对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.main_window = parent
        self.setup_ui()

    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("卡账管理")
        self.setMinimumSize(900, 600)
        self.resize(900, 600)
        self.setModal(False)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # 创建标签页
        self.tab_widget = QTabWidget()

        # 手机卡号管理标签页
        self.phone_card_tab = PhoneCardManagerWidget(self.main_window)
        self.tab_widget.addTab(self.phone_card_tab, "📱 手机卡号管理")

        # APP账号管理标签页
        self.app_account_tab = AppAccountManagerWidget(self.main_window)
        self.tab_widget.addTab(self.app_account_tab, "📱 APP账号管理")

        layout.addWidget(self.tab_widget)

        # 底部按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.close)
        button_layout.addWidget(self.close_btn)

        layout.addLayout(button_layout)

    def switch_to_app_tab_with_filter(self, phone_number):
        """切换到APP账号管理标签页并设置筛选"""
        try:
            # 切换到APP账号管理标签页
            self.tab_widget.setCurrentWidget(self.app_account_tab)
            # 设置筛选条件
            self.app_account_tab.set_phone_filter(phone_number)
            if hasattr(self.main_window, 'log'):
                self.main_window.log(f"已切换到APP账号管理，筛选手机号: {phone_number}")
        except Exception as e:
            if hasattr(self.main_window, 'log'):
                self.main_window.log(f"切换标签页失败: {str(e)}")

        # 设置样式
        self.setStyleSheet("""
            QDialog {
                background: white;
            }
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                border-radius: 4px;
                background: white;
            }
            QTabBar::tab {
                background: #f0f0f0;
                border: 1px solid #c0c0c0;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background: white;
                border-bottom-color: white;
            }
            QPushButton {
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                font-size: 13px;
                font-weight: 500;
                background: #6c757d;
                color: white;
            }
            QPushButton:hover {
                background: #5a6268;
            }
        """)


def handle_exception(exc_type, exc_value, exc_traceback):
    """全局异常处理器"""
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return

    import traceback
    error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
    print(f"未处理的异常: {error_msg}")

    # 尝试显示错误对话框
    try:
        from PyQt6.QtWidgets import QMessageBox, QApplication
        if QApplication.instance():
            QMessageBox.critical(None, "程序错误",
                               f"程序遇到未处理的错误:\n{str(exc_value)}\n\n请重启程序。")
    except:
        pass

class ActionMaintenanceDialog(QDialog):
    """执行动作维护对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.main_window = parent
        self.setup_ui()
        self.load_actions()

    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("执行动作维护")
        self.setFixedSize(500, 400)
        self.setModal(True)

        layout = QVBoxLayout(self)
        layout.setSpacing(15)

        # 说明文字
        info_label = QLabel("管理运行任务配置中的执行动作选项")
        info_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #333; margin-bottom: 10px;")
        layout.addWidget(info_label)

        # 动作列表
        list_layout = QVBoxLayout()

        list_label = QLabel("当前动作列表:")
        list_label.setStyleSheet("font-weight: bold; font-size: 13px; color: #333;")
        list_layout.addWidget(list_label)

        self.action_list_widget = QListWidget()
        self.action_list_widget.setStyleSheet("""
            QListWidget {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 5px;
                font-size: 13px;
                background: white;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
                border-radius: 4px;
                margin: 2px;
            }
            QListWidget::item:hover {
                background: #f0f8ff;
            }
            QListWidget::item:selected {
                background: #4facfe;
                color: white;
            }
        """)
        list_layout.addWidget(self.action_list_widget)

        layout.addLayout(list_layout)

        # 操作按钮
        button_layout = QHBoxLayout()

        self.add_btn = QPushButton("➕ 添加")
        self.add_btn.clicked.connect(self.add_action)

        self.edit_btn = QPushButton("✏️ 编辑")
        self.edit_btn.clicked.connect(self.edit_action)
        self.edit_btn.setEnabled(False)

        self.delete_btn = QPushButton("🗑️ 删除")
        self.delete_btn.clicked.connect(self.delete_action)
        self.delete_btn.setEnabled(False)

        button_layout.addWidget(self.add_btn)
        button_layout.addWidget(self.edit_btn)
        button_layout.addWidget(self.delete_btn)
        button_layout.addStretch()

        layout.addLayout(button_layout)

        # 底部按钮
        bottom_layout = QHBoxLayout()
        bottom_layout.addStretch()

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)

        self.save_btn = QPushButton("保存")
        self.save_btn.clicked.connect(self.save_actions)
        self.save_btn.setDefault(True)

        bottom_layout.addWidget(self.cancel_btn)
        bottom_layout.addWidget(self.save_btn)

        layout.addLayout(bottom_layout)

        # 连接选择变化事件
        self.action_list_widget.itemSelectionChanged.connect(self.on_selection_changed)

        # 设置样式
        self.setStyleSheet("""
            QDialog {
                background: white;
            }
            QPushButton {
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                font-size: 13px;
                font-weight: 500;
                min-width: 80px;
            }
            QPushButton#save_btn {
                background: #4facfe;
                color: white;
            }
            QPushButton#save_btn:hover {
                background: #3d8bfe;
            }
            QPushButton#cancel_btn {
                background: #6c757d;
                color: white;
            }
            QPushButton#cancel_btn:hover {
                background: #5a6268;
            }
            QPushButton#add_btn {
                background: #28a745;
                color: white;
            }
            QPushButton#add_btn:hover {
                background: #218838;
            }
            QPushButton#edit_btn {
                background: #ffc107;
                color: #212529;
            }
            QPushButton#edit_btn:hover {
                background: #e0a800;
            }
            QPushButton#delete_btn {
                background: #dc3545;
                color: white;
            }
            QPushButton#delete_btn:hover {
                background: #c82333;
            }
        """)

        self.save_btn.setObjectName("save_btn")
        self.cancel_btn.setObjectName("cancel_btn")
        self.add_btn.setObjectName("add_btn")
        self.edit_btn.setObjectName("edit_btn")
        self.delete_btn.setObjectName("delete_btn")

    def load_actions(self):
        """加载动作列表"""
        self.action_list_widget.clear()
        if hasattr(self.main_window, 'action_list'):
            for action in self.main_window.action_list:
                self.action_list_widget.addItem(action)

    def on_selection_changed(self):
        """选择变化事件"""
        has_selection = len(self.action_list_widget.selectedItems()) > 0
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)

    def add_action(self):
        """添加动作"""
        from PyQt6.QtWidgets import QInputDialog
        text, ok = QInputDialog.getText(self, "添加执行动作", "请输入新的执行动作名称:")
        if ok and text.strip():
            action_name = text.strip()
            # 检查是否重复
            existing_actions = [self.action_list_widget.item(i).text()
                              for i in range(self.action_list_widget.count())]
            if action_name in existing_actions:
                QMessageBox.warning(self, "警告", "该动作名称已存在！")
                return

            self.action_list_widget.addItem(action_name)

    def edit_action(self):
        """编辑动作"""
        from PyQt6.QtWidgets import QInputDialog
        current_item = self.action_list_widget.currentItem()
        if not current_item:
            return

        current_text = current_item.text()
        text, ok = QInputDialog.getText(self, "编辑执行动作", "请输入新的执行动作名称:", text=current_text)
        if ok and text.strip():
            new_action_name = text.strip()
            # 检查是否重复（排除自己）
            existing_actions = [self.action_list_widget.item(i).text()
                              for i in range(self.action_list_widget.count())
                              if self.action_list_widget.item(i) != current_item]
            if new_action_name in existing_actions:
                QMessageBox.warning(self, "警告", "该动作名称已存在！")
                return

            current_item.setText(new_action_name)

    def delete_action(self):
        """删除动作"""
        current_item = self.action_list_widget.currentItem()
        if not current_item:
            return

        action_name = current_item.text()
        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除执行动作 '{action_name}' 吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            row = self.action_list_widget.row(current_item)
            self.action_list_widget.takeItem(row)

    def save_actions(self):
        """保存动作列表"""
        # 获取当前列表中的所有动作
        actions = [self.action_list_widget.item(i).text()
                  for i in range(self.action_list_widget.count())]

        if not actions:
            QMessageBox.warning(self, "警告", "动作列表不能为空！")
            return

        # 更新主窗口的动作列表
        if hasattr(self.main_window, 'action_list'):
            self.main_window.action_list = actions
            self.main_window.save_config()
            self.main_window.log(f"执行动作列表已更新: {', '.join(actions)}")
            QMessageBox.information(self, "保存成功", "执行动作列表已保存！")
            self.accept()


def main():
    """主程序入口"""
    try:
        # 设置全局异常处理器
        sys.excepthook = handle_exception

        app = QApplication(sys.argv)

        # 设置应用程序信息
        app.setApplicationName("设备管理程序")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("DeviceManager")

        # 设置应用程序图标（如果有的话）
        # app.setWindowIcon(QIcon("icon.png"))

        # 检查必要的依赖
        try:
            import psutil  # noqa: F401
        except ImportError:
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.warning(None, "依赖缺失",
                              "缺少psutil模块，请运行：pip install psutil")
            return

        # 创建主窗口
        window = DeviceManager()
        window.show()

        print("设备管理程序已启动")
        print("如需退出，请关闭窗口或按Ctrl+C")

        # 运行应用程序
        sys.exit(app.exec())

    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"程序启动失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
